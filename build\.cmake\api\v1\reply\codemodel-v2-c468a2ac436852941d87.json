{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-3e03add4965ce1802077.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Debug-df75ece2c6f9db605153.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-Debug-4466944f919b7db2ccf0.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Debug-59a55ac10a1206734713.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Debug-78b31475697dab13199a.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-Debug-480fefee164a6b811231.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-3e03add4965ce1802077.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Release-5127cc795337137468e8.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-Release-1d8b42568b963e5f0680.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Release-3319e1d6ba193e9390c4.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Release-bc862cbba8fd4d2abdc5.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-Release-06a4fa08238491c59ebb.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-3e03add4965ce1802077.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-MinSizeRel-bc8b3a20f51b5cdfdd86.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-MinSizeRel-eb3ad96633e90d36ffbd.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-MinSizeRel-03aa87045e5430f7c669.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-MinSizeRel-856bc0b6d5237bb07cce.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-MinSizeRel-0645841af55d95e3db0c.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3e03add4965ce1802077.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-RelWithDebInfo-1d38d415f18eed8e1ff6.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-RelWithDebInfo-dfcc4dac3b2814349d7b.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-RelWithDebInfo-4d1ad03bdb976317b06f.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-RelWithDebInfo-007efa71c6e2a67abca3.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-RelWithDebInfo-400664f8d610f08baa5c.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/aim_bot/build", "source": "C:/Users/<USER>/Desktop/aim_bot"}, "version": {"major": 2, "minor": 4}}