#include "gpu_preprocessor.h"
#include <iostream>
#include <fstream>
#include <vector>

GPUPreprocessor::GPUPreprocessor() {
}

GPUPreprocessor::~GPUPreprocessor() {
    Cleanup();
}

bool GPUPreprocessor::Initialize(ComPtr<ID3D11Device> device, const PreprocessParams& params) {
    std::cout << "正在初始化GPU预处理器..." << std::endl;

    m_device = device;
    m_device->GetImmediateContext(&m_context);
    m_params = params;

    // 根据配置计算感兴趣区域
    CalculateROI();
    ValidateROI();

    // 初始化计算着色器
    if (!InitializeComputeShader()) {
        std::cout << "计算着色器初始化失败" << std::endl;
        return false;
    }

    // 创建预处理纹理
    if (!CreatePreprocessingTextures()) {
        std::cout << "预处理纹理创建失败" << std::endl;
        return false;
    }

    // 创建常量缓冲区
    if (!CreateConstantBuffer()) {
        std::cout << "常量缓冲区创建失败" << std::endl;
        return false;
    }

    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "屏幕尺寸: " << params.screenWidth << "x" << params.screenHeight << std::endl;
    std::cout << "感兴趣区域: (" << m_roiX << ", " << m_roiY << ") " << m_roiWidth << "x" << m_roiHeight << std::endl;

    return true;
}

bool GPUPreprocessor::InitializeComputeShader() {
    // 从HLSL源码编译计算着色器
    const char* shaderSource = R"(
        cbuffer PreprocessConstants : register(b0)
        {
            float screenWidth;
            float screenHeight;
            float roiX;
            float roiY;

            float roiWidth;
            float roiHeight;
            float normalizationScale;
            float meanR;

            float meanG;
            float meanB;
            float stdR;
            float stdG;

            float stdB;
            float3 padding;
        };

        Texture2D<float4> inputTexture : register(t0);
        RWTexture2D<float4> outputTexture : register(u0);

        [numthreads(16, 16, 1)]
        void CSMain(uint3 id : SV_DispatchThreadID)
        {
            // Check bounds
            if (id.x >= roiWidth || id.y >= roiHeight)
                return;

            // Calculate source coordinates in the full screen texture
            int2 sourceCoord = int2(roiX + id.x, roiY + id.y);

            // Clamp to screen bounds
            sourceCoord = clamp(sourceCoord, int2(0, 0), int2(screenWidth - 1, screenHeight - 1));

            // Load input pixel directly
            // Note: inputTexture is DXGI_FORMAT_B8G8R8A8_UNORM (BGRA byte format)
            // D3D11 automatically converts byte values (0-255) to float (0.0-1.0)
            float4 inputColor = inputTexture.Load(int3(sourceCoord.x, sourceCoord.y, 0));

            // Try direct RGB mapping first (no channel swapping)
            // If DXGI handles byte order correctly, we might not need manual swapping
            float3 rgbColor = float3(inputColor.r, inputColor.g, inputColor.b);

            // Apply normalization: already in 0-1 range from UNORM format
            rgbColor = rgbColor * normalizationScale;

            // Write to output texture
            outputTexture[id.xy] = float4(rgbColor, 1.0);
        }
    )";
    
    ComPtr<ID3DBlob> shaderBlob;
    ComPtr<ID3DBlob> errorBlob;
    
    HRESULT hr = D3DCompile(
        shaderSource,
        strlen(shaderSource),
        nullptr,
        nullptr,
        nullptr,
        "CSMain",
        "cs_5_0",
        D3DCOMPILE_ENABLE_STRICTNESS,
        0,
        &shaderBlob,
        &errorBlob
    );
    
    if (FAILED(hr)) {
        if (errorBlob) {
            std::cout << "着色器编译错误: " << (char*)errorBlob->GetBufferPointer() << std::endl;
        }
        return false;
    }

    hr = m_device->CreateComputeShader(
        shaderBlob->GetBufferPointer(),
        shaderBlob->GetBufferSize(),
        nullptr,
        &m_preprocessShader
    );

    if (FAILED(hr)) {
        std::cout << "计算着色器创建失败" << std::endl;
        return false;
    }
    
    return true;
}

bool GPUPreprocessor::CreatePreprocessingTextures() {
    // Create output texture for preprocessed data (ROI size)
    D3D11_TEXTURE2D_DESC textureDesc = {};
    textureDesc.Width = m_roiWidth;
    textureDesc.Height = m_roiHeight;
    textureDesc.MipLevels = 1;
    textureDesc.ArraySize = 1;
    textureDesc.Format = DXGI_FORMAT_R32G32B32A32_FLOAT; // Float format for model input
    textureDesc.SampleDesc.Count = 1;
    textureDesc.SampleDesc.Quality = 0;
    textureDesc.Usage = D3D11_USAGE_DEFAULT;
    textureDesc.BindFlags = D3D11_BIND_UNORDERED_ACCESS | D3D11_BIND_SHADER_RESOURCE;
    textureDesc.CPUAccessFlags = 0;
    textureDesc.MiscFlags = 0;

    HRESULT hr = m_device->CreateTexture2D(&textureDesc, nullptr, &m_preprocessedTexture);
    if (FAILED(hr)) {
        std::cout << "预处理纹理创建失败" << std::endl;
        return false;
    }

    // 为输出纹理创建UAV
    D3D11_UNORDERED_ACCESS_VIEW_DESC uavDesc = {};
    uavDesc.Format = textureDesc.Format;
    uavDesc.ViewDimension = D3D11_UAV_DIMENSION_TEXTURE2D;
    uavDesc.Texture2D.MipSlice = 0;

    hr = m_device->CreateUnorderedAccessView(m_preprocessedTexture.Get(), &uavDesc, &m_outputUAV);
    if (FAILED(hr)) {
        std::cout << "输出UAV创建失败" << std::endl;
        return false;
    }

    return true;
}

bool GPUPreprocessor::CreateConstantBuffer() {
    D3D11_BUFFER_DESC bufferDesc = {};
    bufferDesc.ByteWidth = sizeof(ShaderConstants);
    bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
    bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    bufferDesc.MiscFlags = 0;

    std::cout << "正在创建常量缓冲区，大小: " << sizeof(ShaderConstants) << " 字节" << std::endl;

    HRESULT hr = m_device->CreateBuffer(&bufferDesc, nullptr, &m_constantBuffer);
    if (FAILED(hr)) {
        std::cout << "常量缓冲区创建失败。HRESULT: 0x" << std::hex << hr << std::endl;
        return false;
    }

    return UpdateConstantBuffer();
}

bool GPUPreprocessor::UpdateConstantBuffer() {
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = m_context->Map(m_constantBuffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);
    if (FAILED(hr)) {
        std::cout << "常量缓冲区映射失败" << std::endl;
        return false;
    }

    ShaderConstants* constants = (ShaderConstants*)mappedResource.pData;
    constants->screenWidth = (float)m_params.screenWidth;
    constants->screenHeight = (float)m_params.screenHeight;
    constants->roiX = (float)m_roiX;
    constants->roiY = (float)m_roiY;
    constants->roiWidth = (float)m_roiWidth;
    constants->roiHeight = (float)m_roiHeight;
    constants->normalizationScale = m_params.normalizationScale;
    constants->meanR = m_params.meanR;
    constants->meanG = m_params.meanG;
    constants->meanB = m_params.meanB;
    constants->stdR = m_params.stdR;
    constants->stdG = m_params.stdG;
    constants->stdB = m_params.stdB;

    m_context->Unmap(m_constantBuffer.Get(), 0);
    return true;
}

bool GPUPreprocessor::CreateShaderViews(ComPtr<ID3D11Texture2D> inputTexture) {
    // Create SRV for input texture
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);
    
    D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
    srvDesc.Format = inputDesc.Format;  // Keep original format
    srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
    srvDesc.Texture2D.MipLevels = 1;
    srvDesc.Texture2D.MostDetailedMip = 0;
    
    HRESULT hr = m_device->CreateShaderResourceView(inputTexture.Get(), &srvDesc, &m_inputSRV);
    if (FAILED(hr)) {
        std::cout << "输入SRV创建失败。HRESULT: 0x" << std::hex << hr << std::endl;
        return false;
    }

    // Create sampler state if not already created
    if (!m_sampler) {
        D3D11_SAMPLER_DESC samplerDesc = {};
        samplerDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_POINT;
        samplerDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
        samplerDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
        samplerDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
        samplerDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
        samplerDesc.MinLOD = 0;
        samplerDesc.MaxLOD = D3D11_FLOAT32_MAX;

        hr = m_device->CreateSamplerState(&samplerDesc, &m_sampler);
        if (FAILED(hr)) {
            std::cout << "采样器状态创建失败" << std::endl;
            return false;
        }
    }

    return true;
}

bool GPUPreprocessor::PreprocessTexture(ComPtr<ID3D11Texture2D> inputTexture, ComPtr<ID3D11Texture2D>& outputTexture) {
    if (!inputTexture || !m_preprocessShader) {
        std::cout << "输入纹理无效或着色器未初始化" << std::endl;
        return false;
    }

    // Update screen dimensions if changed
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);

    if (inputDesc.Width != m_params.screenWidth || inputDesc.Height != m_params.screenHeight) {
        m_params.screenWidth = inputDesc.Width;
        m_params.screenHeight = inputDesc.Height;
        CalculateROI();
        ValidateROI();
        UpdateConstantBuffer();
    }

    // Input texture validation passed

    // 检查输入纹理是否有SHADER_RESOURCE绑定标志
    if (!(inputDesc.BindFlags & D3D11_BIND_SHADER_RESOURCE)) {
        std::cout << "输入纹理没有SHADER_RESOURCE绑定标志！" << std::endl;
        return false;
    }

    // Create shader resource view for input (only if texture changed)
    if (m_cachedInputTexture.Get() != inputTexture.Get()) {
        if (!CreateShaderViews(inputTexture)) {
            return false;
        }
        m_cachedInputTexture = inputTexture;
    }

    // Set compute shader and resources
    m_context->CSSetShader(m_preprocessShader.Get(), nullptr, 0);
    m_context->CSSetConstantBuffers(0, 1, m_constantBuffer.GetAddressOf());
    m_context->CSSetShaderResources(0, 1, m_inputSRV.GetAddressOf());
    m_context->CSSetUnorderedAccessViews(0, 1, m_outputUAV.GetAddressOf(), nullptr);

    // Dispatch compute shader (based on ROI size)
    UINT dispatchX = (m_roiWidth + 15) / 16;  // 16x16 thread groups
    UINT dispatchY = (m_roiHeight + 15) / 16;

    // Dispatching compute shader for ROI processing

    m_context->Dispatch(dispatchX, dispatchY, 1);

    // Unbind resources
    ID3D11ShaderResourceView* nullSRV = nullptr;
    ID3D11UnorderedAccessView* nullUAV = nullptr;
    m_context->CSSetShaderResources(0, 1, &nullSRV);
    m_context->CSSetUnorderedAccessViews(0, 1, &nullUAV, nullptr);

    // Return preprocessed texture
    outputTexture = m_preprocessedTexture;

    return true;
}

void GPUPreprocessor::CalculateROI() {
    switch (m_params.roiConfig.mode) {
        case ROIConfig::CENTER_FIXED:
            m_roiWidth = m_params.roiConfig.fixedWidth;
            m_roiHeight = m_params.roiConfig.fixedHeight;
            m_roiX = (m_params.screenWidth - m_roiWidth) / 2;
            m_roiY = (m_params.screenHeight - m_roiHeight) / 2;
            break;

        case ROIConfig::CENTER_RATIO:
            m_roiWidth = (int)(m_params.screenWidth * m_params.roiConfig.widthRatio);
            m_roiHeight = (int)(m_params.screenHeight * m_params.roiConfig.heightRatio);
            m_roiX = (m_params.screenWidth - m_roiWidth) / 2;
            m_roiY = (m_params.screenHeight - m_roiHeight) / 2;
            break;

        case ROIConfig::CUSTOM_RECT:
            m_roiX = m_params.roiConfig.x;
            m_roiY = m_params.roiConfig.y;
            m_roiWidth = m_params.roiConfig.width;
            m_roiHeight = m_params.roiConfig.height;
            break;
    }
}

void GPUPreprocessor::ValidateROI() {
    // Ensure ROI is within screen bounds
    if (m_roiX < 0) m_roiX = 0;
    if (m_roiY < 0) m_roiY = 0;

    if (m_roiX + m_roiWidth > m_params.screenWidth) {
        m_roiWidth = m_params.screenWidth - m_roiX;
    }
    if (m_roiY + m_roiHeight > m_params.screenHeight) {
        m_roiHeight = m_params.screenHeight - m_roiY;
    }

    // Ensure minimum size
    if (m_roiWidth < 1) m_roiWidth = 1;
    if (m_roiHeight < 1) m_roiHeight = 1;
}

bool GPUPreprocessor::UpdateROI(const ROIConfig& newROI) {
    m_params.roiConfig = newROI;
    CalculateROI();
    ValidateROI();

    // Recreate textures with new ROI size
    m_preprocessedTexture.Reset();
    m_outputUAV.Reset();

    if (!CreatePreprocessingTextures()) {
        std::cout << "ROI更新后纹理重新创建失败" << std::endl;
        return false;
    }

    // Update constant buffer
    return UpdateConstantBuffer();
}

void GPUPreprocessor::GetCurrentROI(int& x, int& y, int& width, int& height) const {
    x = m_roiX;
    y = m_roiY;
    width = m_roiWidth;
    height = m_roiHeight;
}

void GPUPreprocessor::Cleanup() {
    m_inputSRV.Reset();
    m_outputUAV.Reset();
    m_constantBuffer.Reset();
    m_preprocessedTexture.Reset();
    m_preprocessShader.Reset();
    m_context.Reset();
    m_device.Reset();
}
