{"artifacts": [{"path": "Debug/PIDControllerTest.exe"}, {"path": "Debug/PIDControllerTest.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 99, "parent": 0}, {"command": 1, "file": 0, "line": 29, "parent": 0}, {"command": 2, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd"}, {"fragment": "-std:c++17"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/aim_bot/include"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/aim_bot/NetConfig"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/aim_bot/src/kmbox"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/include"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/build/native/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\aim_bot\\packages\\Microsoft.AI.DirectML.1.15.4\\bin\\x64-win", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\aim_bot\\packages\\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\\runtimes\\win-x64\\native", "role": "libraryPath"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "PIDControllerTest", "nameOnDisk": "PIDControllerTest.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/pid_controller_test.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/pid_controller.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}