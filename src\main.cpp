#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>

// 核心项目头文件
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"
#include "mouse_controller.h"
#include "pid_controller.h"

using Microsoft::WRL::ComPtr;

// 删除了RunSimpleScreenCapture和RunRealtimeInference函数

// 自瞄系统 - 完整的FPS游戏自瞄程序
bool RunAimBotSystem() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "测试流程: 屏幕捕获 -> GPU预处理 -> AI推理 -> 目标选择 -> PID控制 -> 鼠标移动" << std::endl;
    std::cout << "控制说明: F1-启用自瞄 | F2-自动开火 | 右键-激活瞄准 | ESC-退出" << std::endl;

    // 初始化屏幕捕获
    std::cout << "正在初始化屏幕捕获..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "屏幕捕获初始化失败" << std::endl;
        return false;
    }
    std::cout << "屏幕捕获初始化成功" << std::endl;

    // 初始化GPU预处理器
    std::cout << "正在初始化GPU预处理器..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);
    params.normalizationScale = 1.0f;
    params.meanR = 0.0f;
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "GPU预处理器初始化失败" << std::endl;
        return false;
    }

    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "感兴趣区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // 初始化推理引擎
    std::cout << "正在初始化推理引擎..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "推理引擎初始化失败" << std::endl;
        return false;
    }
    std::cout << "推理引擎初始化成功" << std::endl;

    // 初始化GPU渲染器（可选显示）
    std::cout << "正在初始化GPU渲染器..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;
    renderConfig.showBoundingBoxes = true;

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "GPU渲染器初始化失败" << std::endl;
        return false;
    }
    std::cout << "GPU渲染器初始化成功" << std::endl;

    // 初始化显示窗口（可选）
    std::cout << "正在初始化显示窗口..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "显示窗口初始化失败" << std::endl;
        return false;
    }
    std::cout << "显示窗口初始化成功" << std::endl;

    // 初始化鼠标控制器
    std::cout << "正在初始化鼠标控制器..." << std::endl;
    auto mouseController = MouseControllerFactory::Create("kmbox");
    if (!mouseController) {
        std::cout << "鼠标控制器创建失败" << std::endl;
        return false;
    }

    std::string mouseConfig = R"({
        "ip": "*************",
        "port": "8808",
        "mac": "62587019",
        "enableEncryption": true
    })";

    if (!mouseController->Initialize(mouseConfig)) {
        std::cout << "鼠标控制器初始化失败: " << mouseController->GetLastErrorMessage() << std::endl;
        return false;
    }
    std::cout << "鼠标控制器初始化成功: " << mouseController->GetControllerType() << std::endl;

    // 初始化PID控制器
    std::cout << "正在初始化PID控制器..." << std::endl;
    auto pidController = std::make_unique<PIDController>(
        PIDControllerFactory::CreateAdaptiveResponse()
    );

    // 设置目标到达回调（自动开火）
    bool autoFireEnabled = false;
    pidController->SetTargetReachedCallback([&mouseController, &autoFireEnabled]() {
        if (autoFireEnabled) {
            mouseController->ClickMouseButton(MouseController::MouseButton::Left, 50);
            std::cout << "? 自动开火！" << std::endl;
        }
    });

    std::cout << "PID控制器初始化成功" << std::endl;

    // 自瞄系统配置
    bool aimEnabled = false;
    // 计算屏幕绝对中心坐标（ROI中心在屏幕中的位置）- 优化：使用乘法替代除法
    Vector2D screenAbsoluteCenter(roiX + roiWidth * 0.5f, roiY + roiHeight * 0.5f);
    Vector2D roiCenter(160.0f, 160.0f);     // 320x320区域的中心（相对于ROI）
    float aimFOV = 120.0f;                   // 自瞄视野范围
    float yOffsetRatioHead = -0.2f;          // 头部目标Y轴偏移比例（负值向上偏移，相对于框高度）
    float yOffsetRatioBody = 0.0f;           // 身体目标Y轴偏移比例

    std::cout << "\n=== 自瞄系统就绪 ===" << std::endl;
    std::cout << "ROI区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;
    std::cout << "屏幕绝对中心: (" << screenAbsoluteCenter.x << ", " << screenAbsoluteCenter.y << ")" << std::endl;
    std::cout << "ROI相对中心: (" << roiCenter.x << ", " << roiCenter.y << ")" << std::endl;
    std::cout << "自瞄视野: " << aimFOV << " 像素" << std::endl;
    std::cout << "控制说明:" << std::endl;
    std::cout << "  F1 - 启用/禁用自瞄" << std::endl;
    std::cout << "  F2 - 启用/禁用自动开火" << std::endl;
    std::cout << "  右键 - 优先瞄准头部(0类)，无头部时瞄准身体(1类)" << std::endl;
    std::cout << "  下侧键 - 优先瞄准身体(1类)，无身体时瞄准头部(0类)" << std::endl;
    std::cout << "  ESC - 退出程序" << std::endl;
    std::cout << "===================" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    // 智能目标选择函数 - 支持按键优先级
    auto SelectBestTarget = [&](const std::vector<Detection>& detections) -> Detection* {
        if (detections.empty()) return nullptr;

        // 检测当前按键状态
        bool rightButtonPressed = GetAsyncKeyState(VK_RBUTTON) & 0x8000;   // 右键
        bool xButton1Pressed = GetAsyncKeyState(VK_XBUTTON1) & 0x8000;     // 下侧键

        // 确定优先级策略
        int primaryClass = -1;   // 首选类别
        int secondaryClass = -1; // 次选类别

        if (rightButtonPressed) {
            primaryClass = 0;    // 右键优先瞄准头部（0号类别）
            secondaryClass = 1;  // 没有头部时瞄准身体（1号类别）
        } else if (xButton1Pressed) {
            primaryClass = 1;    // 下侧键优先瞄准身体（1号类别）
            secondaryClass = 0;  // 没有身体时瞄准头部（0号类别）
        } else {
            return nullptr;      // 没有按键按下，不进行瞄准
        }

        // 分别收集首选和次选类别的目标
        std::vector<const Detection*> primaryTargets;
        std::vector<const Detection*> secondaryTargets;

        for (const auto& detection : detections) {
            // detection.x和detection.y已经是识别框的中心坐标，直接使用
            Vector2D targetCenterROI(detection.x, detection.y);

            // 计算距离ROI中心的距离
            float distance = (targetCenterROI - roiCenter).Length();

            // 检查是否在自瞄视野范围内
            if (distance > aimFOV) continue;

            // 根据类别分类
            if (detection.classId == primaryClass) {
                primaryTargets.push_back(&detection);
            } else if (detection.classId == secondaryClass) {
                secondaryTargets.push_back(&detection);
            }
        }

        // 选择最佳目标的函数
        auto FindClosestTarget = [&](const std::vector<const Detection*>& targets) -> const Detection* {
            if (targets.empty()) return nullptr;

            const Detection* bestTarget = nullptr;
            float minDistance = FLT_MAX;

            for (const auto* detection : targets) {
                // detection->x和detection->y已经是识别框的中心坐标
                Vector2D targetCenterROI(detection->x, detection->y);
                float distance = (targetCenterROI - roiCenter).Length();

                // 综合考虑距离和置信度
                float score = detection->confidence * (aimFOV - distance) / aimFOV;

                if (distance < minDistance && score > 0.7f) { // 最小置信度阈值
                    minDistance = distance;
                    bestTarget = detection;
                }
            }

            return bestTarget;
        };

        // 优先选择首选类别中最近的目标
        const Detection* target = FindClosestTarget(primaryTargets);
        if (target) {
            return const_cast<Detection*>(target);
        }

        // 首选类别没有目标，选择次选类别中最近的目标
        target = FindClosestTarget(secondaryTargets);
        return const_cast<Detection*>(target);
    };

    std::cout << "\n开始运行自瞄系统..." << std::endl;

    while (!displayWindow.ShouldClose()) {
        // 处理窗口消息
        displayWindow.ProcessMessages();
        frameCount++;

        // 检查用户输入
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            aimEnabled = !aimEnabled;
            std::cout << "自瞄状态: " << (aimEnabled ? " 启用" : " 禁用") << std::endl;
            if (!aimEnabled) pidController->Reset();
            Sleep(200); // 防止重复触发
        }

        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            autoFireEnabled = !autoFireEnabled;
            std::cout << "自动开火: " << (autoFireEnabled ? " 启用" : " 禁用") << std::endl;
            Sleep(200);
        }

        // 高精度计时
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. 捕获屏幕
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. GPU预处理
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. AI推理（GPU上进行，包含NMS后处理）
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. 自瞄逻辑（CPU上进行）
        QueryPerformanceCounter(&start);
        double aimTime = 0.0;
        static Detection* currentTarget = nullptr;
        static Vector2D lastError(0.0f, 0.0f);
        static Vector2D lastOutput(0.0f, 0.0f);

        if (aimEnabled && mouseController->IsConnected()) {
            // 选择最佳目标（按键检测已集成在SelectBestTarget函数中）
            currentTarget = SelectBestTarget(detections);

            if (currentTarget) {
                // detection.x和detection.y已经是识别框的中心坐标，应用Y轴偏移
                float yOffsetRatio = (currentTarget->classId == 0) ? yOffsetRatioHead : yOffsetRatioBody;
                float yOffset = currentTarget->height * yOffsetRatio;  // 偏移量 = 框高度 × 偏移比例
                Vector2D targetCenterROI(currentTarget->x, currentTarget->y + yOffset);

                // 计算误差（目标位置 - ROI中心位置）
                Vector2D error = targetCenterROI - roiCenter;
                lastError = error;

                // 显示当前瞄准的目标类型和按键状态
                std::string targetType = (currentTarget->classId == 0) ? "头部" : "身体";
                std::string activeKey = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) ? "右键" : "下侧键";
                std::cout << "\r🎯 " << activeKey << "->" << targetType << " ROI坐标: (" << targetCenterROI.x << ", " << targetCenterROI.y
                         << ") 误差: (" << error.x << ", " << error.y << ")                    " << std::flush;

                // 使用PID控制器计算移动向量
                Vector2D moveVector = pidController->Calculate(error);
                lastOutput = moveVector;

                // 执行鼠标移动
                if (moveVector.Length() > 0.5f) {
                    mouseController->MoveMouse(
                        static_cast<int>(moveVector.x),
                        static_cast<int>(moveVector.y),
                        MouseController::MoveType::Smooth,
                        10  // 10ms快速移动
                    );
                }
            }
        }

        QueryPerformanceCounter(&end);
        aimTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 5. 渲染显示（可选）
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
            continue;
        }
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + aimTime + displayTime;

        // 每30帧更新性能信息（原地刷新）
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            // 移动光标到行首并清除当前行
            std::cout << "\r";
            std::cout << "[" << frameCount << "] "
                      << "FPS:" << std::fixed << std::setprecision(0) << fps << " | "
                      << "检测:" << detections.size()
                      << " | 自瞄:" << (aimEnabled ? "ON" : "OFF")
                      << " | 右键:" << (GetAsyncKeyState(VK_RBUTTON) & 0x8000 ? "按下" : "松开")
                      << " | 下侧键:" << (GetAsyncKeyState(VK_XBUTTON1) & 0x8000 ? "按下" : "松开");

            // 如果有目标，显示目标信息
            if (currentTarget) {
                std::string targetType = (currentTarget->classId == 0) ? "头部" : "身体";
                std::string activeKey = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) ? "右键" : "下侧键";
                float yOffsetRatio = (currentTarget->classId == 0) ? yOffsetRatioHead : yOffsetRatioBody;
                float actualOffset = currentTarget->height * yOffsetRatio;
                std::string offsetInfo = "(偏移:" + std::to_string(actualOffset) + "px)";
                std::cout << " | 目标:" << activeKey << "->" << targetType << offsetInfo
                         << " 误差:" << std::setprecision(1) << lastError.Length()
                         << " 输出:" << lastOutput.Length();
            }

            std::cout << "                              " << std::flush;  // 额外空格清除旧内容
        }

        // ESC键退出
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "\n正在关闭自瞄系统..." << std::endl;
    mouseController->Disconnect();
    std::cout << "自瞄系统已关闭" << std::endl;
    return true;
}

int main() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "此应用程序捕获屏幕中心320x320区域" << std::endl;
    std::cout << "并运行AI推理来实时检测头部和身体目标，实现智能自瞄。" << std::endl;
    std::cout << std::endl;

    bool success = RunAimBotSystem();

    std::cout << std::endl;
    if (success) {
        std::cout << "应用程序成功完成！" << std::endl;
    } else {
        std::cout << "应用程序运行失败！" << std::endl;
    }

    std::cout << "按Enter键退出..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
