#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>

// 核心项目头文件
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"

using Microsoft::WRL::ComPtr;

// 简单屏幕捕获测试 - 捕获、预处理和显示
bool RunSimpleScreenCapture() {
    std::cout << "=== 简单屏幕捕获测试 ===" << std::endl;
    std::cout << "测试流程: 屏幕捕获 -> GPU预处理 -> 显示" << std::endl;
    std::cout << "按ESC键退出" << std::endl;

    // 初始化屏幕捕获
    std::cout << "正在初始化屏幕捕获..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "屏幕捕获初始化失败" << std::endl;
        return false;
    }
    std::cout << "屏幕捕获初始化成功" << std::endl;

    // 初始化GPU预处理器，使用中心320x320区域
    std::cout << "正在初始化GPU预处理器..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // 屏幕分辨率
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // 中心320x320区域
    params.normalizationScale = 1.0f;  // 不缩放，保持原始颜色
    params.meanR = 0.0f;  // 不进行均值减法
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // 不进行标准差缩放
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "GPU预处理器初始化失败" << std::endl;
        return false;
    }

    // 打印ROI信息
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "感兴趣区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // 初始化GPU渲染器用于格式转换
    std::cout << "正在初始化GPU渲染器..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = false;
    renderConfig.showBoundingBoxes = false;

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "GPU渲染器初始化失败" << std::endl;
        return false;
    }
    std::cout << "GPU渲染器初始化成功" << std::endl;

    // 初始化GPU显示窗口
    std::cout << "正在初始化GPU显示窗口..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "显示窗口初始化失败" << std::endl;
        return false;
    }
    std::cout << "显示窗口初始化成功" << std::endl;

    std::cout << "\n开始简单屏幕捕获..." << std::endl;
    std::cout << "窗口将显示屏幕中心320x320区域" << std::endl;
    std::cout << "格式: [帧数] 捕获:X毫秒 预处理:X毫秒 渲染:X毫秒 总计:X毫秒 | 帧率: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // 处理窗口消息
        displayWindow.ProcessMessages();

        frameCount++;

        // 高精度计时
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. 捕获屏幕
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. 预处理（提取中心320x320区域并转换BGRA到RGB）
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. 格式转换和渲染（R32G32B32A32_FLOAT -> R8G8B8A8_UNORM）
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        // 简单显示无检测结果，仅进行格式转换
        if (!renderer.RenderDetections(preprocessedTexture.Get(), nullptr, 0, renderedTexture)) {
            continue;
        }

        // 4. 显示渲染纹理
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + displayTime;

        // 每30帧显示性能信息
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            std::cout << "[" << frameCount << "] "
                      << "捕获:" << std::fixed << std::setprecision(2) << captureTime << "毫秒 "
                      << "预处理:" << preprocessTime << "毫秒 "
                      << "渲染:" << displayTime << "毫秒 "
                      << "总计:" << totalTime << "毫秒 | "
                      << "帧率: " << std::setprecision(1) << fps << std::endl;
        }

        // ESC键检查
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "简单屏幕捕获测试完成！" << std::endl;
    return true;
}

// 实时推理与显示
bool RunRealtimeInference() {
    std::cout << "=== 实时推理测试 ===" << std::endl;
    std::cout << "测试流程: 屏幕捕获 -> GPU预处理 -> AI推理 -> 显示" << std::endl;
    std::cout << "按ESC键退出" << std::endl;

    // 初始化屏幕捕获
    std::cout << "正在初始化屏幕捕获..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "屏幕捕获初始化失败" << std::endl;
        return false;
    }
    std::cout << "屏幕捕获初始化成功" << std::endl;

    // 初始化GPU预处理器，使用中心320x320区域
    std::cout << "正在初始化GPU预处理器..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // 屏幕分辨率
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // 中心320x320区域
    params.normalizationScale = 1.0f;  // 暂不缩放
    params.meanR = 0.0f;  // 不进行均值减法
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // 不进行标准差缩放
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "GPU预处理器初始化失败" << std::endl;
        return false;
    }

    // 打印ROI信息
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "感兴趣区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // 初始化推理引擎
    std::cout << "正在初始化推理引擎..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "推理引擎初始化失败" << std::endl;
        return false;
    }
    std::cout << "推理引擎初始化成功" << std::endl;

    // 初始化GPU渲染器用于格式转换和检测显示
    std::cout << "正在初始化GPU渲染器..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;   // 显示十字准星
    renderConfig.showBoundingBoxes = true;  // 显示检测框

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "GPU渲染器初始化失败" << std::endl;
        return false;
    }
    std::cout << "GPU渲染器初始化成功" << std::endl;

    // 初始化GPU显示窗口
    std::cout << "正在初始化GPU显示窗口..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "显示窗口初始化失败" << std::endl;
        return false;
    }
    std::cout << "显示窗口初始化成功" << std::endl;

    std::cout << "\n开始实时推理..." << std::endl;
    std::cout << "窗口将显示中心320x320区域和AI检测结果" << std::endl;
    std::cout << "格式: [帧数] 捕获:X毫秒 预处理:X毫秒 推理:X毫秒 渲染:X毫秒 总计:X毫秒 | 帧率: X | 检测数: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // 处理窗口消息
        displayWindow.ProcessMessages();

        frameCount++;

        // 高精度计时
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. 捕获屏幕
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. 预处理（提取中心320x320区域并转换BGRA到RGB）
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. 运行AI推理
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. 渲染检测结果（使用原始捕获纹理进行显示）
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
            continue;
        }

        // 5. 显示渲染纹理
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + displayTime;

        // 每30帧显示性能信息
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            std::cout << "[" << frameCount << "] "
                      << "捕获:" << std::fixed << std::setprecision(2) << captureTime << "毫秒 "
                      << "预处理:" << preprocessTime << "毫秒 "
                      << "推理:" << inferenceTime << "毫秒 "
                      << "渲染:" << displayTime << "毫秒 "
                      << "总计:" << totalTime << "毫秒 | "
                      << "帧率: " << std::setprecision(1) << fps << " | "
                      << "检测数: " << detections.size() << std::endl;
        }

        // ESC键检查
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "实时推理测试完成！" << std::endl;
    return true;
}

int main() {
    std::cout << "=== 自瞄机器人实时推理应用程序 ===" << std::endl;
    std::cout << "此应用程序捕获屏幕中心320x320区域" << std::endl;
    std::cout << "并运行AI推理来实时检测头部和身体目标。" << std::endl;
    std::cout << std::endl;

    std::cout << "选择测试模式:" << std::endl;
    std::cout << "1. 简单屏幕捕获（无AI推理）" << std::endl;
    std::cout << "2. 实时AI推理与检测显示" << std::endl;
    std::cout << "请输入选择 (1-2): ";

    int choice;
    std::cin >> choice;
    std::cin.ignore(); // 清空输入缓冲区

    bool success = false;

    switch (choice) {
        case 1:
            success = RunSimpleScreenCapture();
            break;
        case 2:
            success = RunRealtimeInference();
            break;
        default:
            std::cout << "无效选择。运行简单屏幕捕获..." << std::endl;
            success = RunSimpleScreenCapture();
            break;
    }

    std::cout << std::endl;
    if (success) {
        std::cout << "应用程序成功完成！" << std::endl;
    } else {
        std::cout << "应用程序运行失败！" << std::endl;
    }

    std::cout << "按Enter键退出..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
