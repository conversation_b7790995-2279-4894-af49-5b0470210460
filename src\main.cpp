#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>

// ImGui头文件
#include "../imgui/imgui.h"
#include "../imgui/imgui_impl_dx11.h"
#include "../imgui/imgui_impl_win32.h"

// 核心项目头文件
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"
#include "mouse_controller.h"
#include "pid_controller.h"
#include "imgui_pid_debugger.h"
#include "mouse_move_thread.h"

using Microsoft::WRL::ComPtr;

// 删除了RunSimpleScreenCapture和RunRealtimeInference函数

// 自瞄系统 - 完整的FPS游戏自瞄程序
bool RunAimBotSystem() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "测试流程: 屏幕捕获 -> GPU预处理 -> AI推理 -> 目标选择 -> PID控制 -> 鼠标移动" << std::endl;
    std::cout << "控制说明: F1-启用自瞄 | F2-自动开火 | 右键-激活瞄准 | ESC-退出" << std::endl;

    // 初始化屏幕捕获
    std::cout << "正在初始化屏幕捕获..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "屏幕捕获初始化失败" << std::endl;
        return false;
    }
    std::cout << "屏幕捕获初始化成功" << std::endl;

    // 初始化GPU预处理器
    std::cout << "正在初始化GPU预处理器..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);
    params.normalizationScale = 1.0f;
    params.meanR = 0.0f;
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "GPU预处理器初始化失败" << std::endl;
        return false;
    }

    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "感兴趣区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // 初始化推理引擎
    std::cout << "正在初始化推理引擎..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "推理引擎初始化失败" << std::endl;
        return false;
    }
    std::cout << "推理引擎初始化成功" << std::endl;

    // 初始化GPU渲染器（可选显示）
    std::cout << "正在初始化GPU渲染器..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;
    renderConfig.showBoundingBoxes = true;

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "GPU渲染器初始化失败" << std::endl;
        return false;
    }
    std::cout << "GPU渲染器初始化成功" << std::endl;

    // 初始化显示窗口（可选）
    std::cout << "正在初始化显示窗口..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "显示窗口初始化失败" << std::endl;
        return false;
    }
    std::cout << "显示窗口初始化成功" << std::endl;

    // 初始状态隐藏窗口
    ShowWindow(displayWindow.GetWindowHandle(), SW_HIDE);

    // 初始化ImGui
    std::cout << "正在初始化ImGui..." << std::endl;
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
    io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable;

    // 禁用ini文件保存
    io.IniFilename = nullptr;

    // 加载中文字体
    ImFontConfig fontConfig;
    fontConfig.FontDataOwnedByAtlas = false;

    // 尝试多个字体路径
    const char* fontPaths[] = {
        "C:\\Windows\\Fonts\\msyh.ttc",  // 微软雅黑
        "C:\\Windows\\Fonts\\simhei.ttf", // 黑体
        "C:\\Windows\\Fonts\\simsun.ttc", // 宋体
        "C:\\Users\\<USER>\\Desktop\\aim_bot\\Login_Title.ttf" // 用户自定义字体
    };

    ImFont* chineseFont = nullptr;
    ImFont* chineseFontBig = nullptr;

    // 尝试加载字体
    for (const char* fontPath : fontPaths) {
        chineseFont = io.Fonts->AddFontFromFileTTF(fontPath, 18.0f, &fontConfig, io.Fonts->GetGlyphRangesChineseFull());
        if (chineseFont != nullptr) {
            chineseFontBig = io.Fonts->AddFontFromFileTTF(fontPath, 24.0f, &fontConfig, io.Fonts->GetGlyphRangesChineseFull());
            std::cout << "成功加载中文字体: " << fontPath << std::endl;
            break;
        }
    }

    // 如果所有字体都加载失败，使用默认字体
    if (chineseFont == nullptr) {
        std::cout << "警告: 无法加载任何中文字体，使用默认字体" << std::endl;
        ImFontConfig defaultConfig;
        io.Fonts->AddFontDefault(&defaultConfig);
        // 添加中文字符范围
        io.Fonts->GetGlyphRangesChineseFull();
    }

    ImGui::StyleColorsDark();

    // 初始化ImGui平台/渲染器后端
    ImGui_ImplWin32_Init(displayWindow.GetWindowHandle());
    ImGui_ImplDX11_Init(capture.GetDevice().Get(), capture.GetContext().Get());

    // 确保字体纹理被上传到GPU
    unsigned char* pixels;
    int width, height;
    io.Fonts->GetTexDataAsRGBA32(&pixels, &width, &height);

    std::cout << "ImGui初始化成功，字体纹理大小: " << width << "x" << height << std::endl;

    // 初始化鼠标控制器
    std::cout << "正在初始化鼠标控制器..." << std::endl;
    auto mouseController = std::shared_ptr<MouseController>(MouseControllerFactory::Create("kmbox").release());
    if (!mouseController) {
        std::cout << "鼠标控制器创建失败" << std::endl;
        return false;
    }

    std::string mouseConfig = R"({
        "ip": "*************",
        "port": "8808",
        "mac": "62587019",
        "enableEncryption": true
    })";

    if (!mouseController->Initialize(mouseConfig)) {
        std::cout << "鼠标控制器初始化失败: " << mouseController->GetLastErrorMessage() << std::endl;
        return false;
    }
    std::cout << "鼠标控制器初始化成功: " << mouseController->GetControllerType() << std::endl;

    // 初始化PID控制器
    std::cout << "正在初始化PID控制器..." << std::endl;
    PIDController::PIDConfig pidConfig;
    // 使用默认配置，用户可以通过调试界面调整
    pidConfig.kp = 0.8f;
    pidConfig.ki = 0.0f;
    pidConfig.kd = 0.0f;
    pidConfig.maxOutput = 100.0f;
    pidConfig.deadZone = 0.0f;

    auto pidController = std::make_unique<PIDController>(pidConfig);

    // 初始化鼠标移动线程
    std::cout << "正在初始化鼠标移动线程..." << std::endl;
    auto mouseMoveThread = std::make_unique<MouseMoveThread>(mouseController);
    mouseMoveThread->Start();
    std::cout << "鼠标移动线程初始化成功" << std::endl;

    // 设置目标到达回调（自动开火）
    bool autoFireEnabled = false;
    pidController->SetTargetReachedCallback([&mouseController, &autoFireEnabled]() {
        if (autoFireEnabled) {
            mouseController->ClickMouseButton(MouseController::MouseButton::Left, 50);
            std::cout << "? 自动开火！" << std::endl;
        }
    });

    std::cout << "PID控制器初始化成功" << std::endl;

    // 自瞄系统配置
    bool aimEnabled = false;
    bool showRenderWindow = false;              // 推理画面窗口显示状态
    bool showPIDDebugger = false;               // PID调试器显示状态
    // 计算屏幕绝对中心坐标（ROI中心在屏幕中的位置）- 优化：使用乘法替代除法
    Vector2D screenAbsoluteCenter(roiX + roiWidth * 0.5f, roiY + roiHeight * 0.5f);
    Vector2D roiCenter(160.0f, 160.0f);     // 320x320区域的中心（相对于ROI）
    float aimFOV = 1200.0f;                   // 自瞄视野范围
    float yOffsetRatioHead = -0.2f;          // 头部目标Y轴偏移比例（负值向上偏移，相对于框高度）
    float yOffsetRatioBody = -0.45f;           // 身体目标Y轴偏移比例

    // 初始化PID调试器
    std::cout << "正在初始化PID调试器..." << std::endl;
    PIDDebugger pidDebugger;
    if (!pidDebugger.Initialize(capture.GetDevice().Get(), nullptr, displayWindow.GetWindowHandle())) {
        std::cout << "PID调试器初始化失败" << std::endl;
        return false;
    }
    pidDebugger.SetPIDController(pidController.get());
    pidDebugger.SetExternalParams(&yOffsetRatioHead, &yOffsetRatioBody);
    std::cout << "PID调试器初始化成功" << std::endl;

    std::cout << "\n=== 自瞄系统就绪 ===" << std::endl;
    std::cout << "ROI区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;
    std::cout << "屏幕绝对中心: (" << screenAbsoluteCenter.x << ", " << screenAbsoluteCenter.y << ")" << std::endl;
    std::cout << "ROI相对中心: (" << roiCenter.x << ", " << roiCenter.y << ")" << std::endl;
    std::cout << "自瞄视野: " << aimFOV << " 像素" << std::endl;
    std::cout << "控制说明:" << std::endl;
    std::cout << "  F1 - 启用/禁用自瞄" << std::endl;
    std::cout << "  F2 - 启用/禁用自动开火" << std::endl;
    std::cout << "  F3 - 显示/隐藏推理画面窗口（置顶显示）" << std::endl;
    std::cout << "  右键 - 优先瞄准头部(0类)，无头部时瞄准身体(1类)" << std::endl;
    std::cout << "  下侧键 - 优先瞄准身体(1类)，无身体时瞄准头部(0类)" << std::endl;
    std::cout << "  F4 - 退出程序" << std::endl;
    std::cout << "  F5 - 显示/隐藏PID调试器" << std::endl;
    std::cout << "===================" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    // 智能目标选择函数 - 支持按键优先级
    auto SelectBestTarget = [&](const std::vector<Detection>& detections) -> Detection* {
        if (detections.empty()) return nullptr;

        // 检测当前按键状态
        bool rightButtonPressed = GetAsyncKeyState(VK_RBUTTON) & 0x8000;   // 右键
        bool xButton1Pressed = GetAsyncKeyState(VK_XBUTTON1) & 0x8000;     // 下侧键

        // 确定优先级策略
        int primaryClass = -1;   // 首选类别
        int secondaryClass = -1; // 次选类别

        if (rightButtonPressed) {
            primaryClass = 1;    // 右键优先瞄准头部（0号类别）
            secondaryClass = 0;  // 没有头部时瞄准身体（1号类别）
        } else if (xButton1Pressed) {
            primaryClass = 0;    // 下侧键优先瞄准身体（1号类别）
            secondaryClass = 1;  // 没有身体时瞄准头部（0号类别）
        } else {
            return nullptr;      // 没有按键按下，不进行瞄准
        }

        // 分别收集首选和次选类别的目标
        std::vector<const Detection*> primaryTargets;
        std::vector<const Detection*> secondaryTargets;

        for (const auto& detection : detections) {
            // detection.x和detection.y已经是识别框的中心坐标，直接使用
            Vector2D targetCenterROI(detection.x, detection.y);

            // 计算距离ROI中心的距离
            float distance = (targetCenterROI - roiCenter).Length();

            // 检查是否在自瞄视野范围内
            if (distance > aimFOV) continue;

            // 根据类别分类
            if (detection.classId == primaryClass) {
                primaryTargets.push_back(&detection);
            } else if (detection.classId == secondaryClass) {
                secondaryTargets.push_back(&detection);
            }
        }

        // 选择最佳目标的函数
        auto FindClosestTarget = [&](const std::vector<const Detection*>& targets) -> const Detection* {
            if (targets.empty()) return nullptr;

            const Detection* bestTarget = nullptr;
            float minDistance = FLT_MAX;

            for (const auto* detection : targets) {
                // detection->x和detection->y已经是识别框的中心坐标
                Vector2D targetCenterROI(detection->x, detection->y);
                float distance = (targetCenterROI - roiCenter).Length();

                // 综合考虑距离和置信度
                float score = detection->confidence * (aimFOV - distance) / aimFOV;

                if (distance < minDistance && score > 0.7f) { // 最小置信度阈值
                    minDistance = distance;
                    bestTarget = detection;
                }
            }

            return bestTarget;
        };

        // 优先选择首选类别中最近的目标
        const Detection* target = FindClosestTarget(primaryTargets);
        if (target) {
            return const_cast<Detection*>(target);
        }

        // 首选类别没有目标，选择次选类别中最近的目标
        target = FindClosestTarget(secondaryTargets);
        return const_cast<Detection*>(target);
    };

    std::cout << "\n开始运行自瞄系统..." << std::endl;

    while (!displayWindow.ShouldClose()) {
        // 处理窗口消息
        displayWindow.ProcessMessages();
        frameCount++;

        // 开始ImGui帧
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // 检查用户输入
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            aimEnabled = !aimEnabled;
            std::cout << "自瞄状态: " << (aimEnabled ? " 启用" : " 禁用") << std::endl;
            if (!aimEnabled) pidController->Reset();
            Sleep(200); // 防止重复触发
        }

        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            autoFireEnabled = !autoFireEnabled;
            std::cout << "自动开火: " << (autoFireEnabled ? " 启用" : " 禁用") << std::endl;
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            showRenderWindow = !showRenderWindow;
            std::cout << "推理画面窗口: " << (showRenderWindow ? " 启用" : " 禁用") << std::endl;

            // 控制窗口显示/隐藏
            if (showRenderWindow) {
                ShowWindow(displayWindow.GetWindowHandle(), SW_SHOW);
                // 设置窗口置顶
                SetWindowPos(displayWindow.GetWindowHandle(), HWND_TOPMOST, 0, 0, 0, 0,
                           SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
            } else {
                ShowWindow(displayWindow.GetWindowHandle(), SW_HIDE);
            }
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_F5) & 0x8000) {
            showPIDDebugger = !showPIDDebugger;
            std::cout << "PID调试器: " << (showPIDDebugger ? " 启用" : " 禁用") << std::endl;
            Sleep(200);
        }

        // 高精度计时
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. 捕获屏幕
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. GPU预处理
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. AI推理（GPU上进行，包含NMS后处理）
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. 自瞄逻辑（CPU上进行）
        QueryPerformanceCounter(&start);
        double aimTime = 0.0;
        static Detection* currentTarget = nullptr;
        static Vector2D lastError(0.0f, 0.0f);
        static Vector2D lastOutput(0.0f, 0.0f);

        if (aimEnabled && mouseController->IsConnected()) {
            // 选择最佳目标（按键检测已集成在SelectBestTarget函数中）
            currentTarget = SelectBestTarget(detections);

            if (currentTarget) {
                // detection.x和detection.y已经是识别框的中心坐标，应用Y轴偏移
                float yOffsetRatio = (currentTarget->classId == 0) ? yOffsetRatioHead : yOffsetRatioBody;
                float yOffset = currentTarget->height * yOffsetRatio;  // 偏移量 = 框高度 × 偏移比例
                Vector2D targetCenterROI(currentTarget->x, currentTarget->y + yOffset);

                // 计算误差（目标位置 - ROI中心位置）
                Vector2D error = targetCenterROI - roiCenter;
                lastError = error;

                // 显示当前瞄准的目标类型和按键状态
                std::string targetType = (currentTarget->classId == 0) ? "头部" : "身体";
                std::string activeKey = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) ? "右键" : "下侧键";
                std::cout << "\r🎯 " << activeKey << "->" << targetType << " ROI坐标: (" << targetCenterROI.x << ", " << targetCenterROI.y
                         << ") 误差: (" << error.x << ", " << error.y << ")                    " << std::flush;

                // 使用带预测的PID控制器计算移动向量（推荐用于移动目标）
                Vector2D moveVector = pidController->CalculateWithPrediction(targetCenterROI);
                lastOutput = moveVector;

                // 将移动指令添加到移动线程队列（非阻塞）
                if (moveVector.Length() > 0.5f) {
                    mouseMoveThread->AddMoveCommand(
                        static_cast<int>(moveVector.x),
                        static_cast<int>(moveVector.y),
                        MouseController::MoveType::Smooth,
                        10  // 10ms快速移动
                    );
                }
            }
        }

        QueryPerformanceCounter(&end);
        aimTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 5. 渲染显示（仅在窗口显示时进行）
        QueryPerformanceCounter(&start);
        double displayTime = 0.0;

        if (showRenderWindow) {
            ID3D11Texture2D* renderedTexture = nullptr;
            if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
                continue;
            }
            displayWindow.Present(renderedTexture);
        }

        // 6. 渲染ImGui PID调试器
        if (showPIDDebugger) {
            pidDebugger.NewFrame();
            pidDebugger.RenderDebugWindow(showPIDDebugger);
            pidDebugger.EndFrame();
        }

        QueryPerformanceCounter(&end);
        displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + aimTime + displayTime;

        // 每30帧更新性能信息（原地刷新）
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            // 移动光标到行首并清除当前行
            std::cout << "\r";
            std::cout << "[" << frameCount << "] "
                      << "FPS:" << std::fixed << std::setprecision(0) << fps << " | "
                      << "检测:" << detections.size()
                      << " | 自瞄:" << (aimEnabled ? "ON" : "OFF")
                      << " | 窗口:" << (showRenderWindow ? "显示" : "隐藏")
                      << " | 右键:" << (GetAsyncKeyState(VK_RBUTTON) & 0x8000 ? "按下" : "松开")
                      << " | 下侧键:" << (GetAsyncKeyState(VK_XBUTTON1) & 0x8000 ? "按下" : "松开");

            // 如果有目标，显示目标信息
            if (currentTarget) {
                std::string targetType = (currentTarget->classId == 0) ? "头部" : "身体";
                std::string activeKey = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) ? "右键" : "下侧键";
                float yOffsetRatio = (currentTarget->classId == 0) ? yOffsetRatioHead : yOffsetRatioBody;
                float actualOffset = currentTarget->height * yOffsetRatio;
                std::string offsetInfo = "(偏移:" + std::to_string(actualOffset) + "px)";
                std::cout << " | 目标:" << activeKey << "->" << targetType << offsetInfo
                         << " 误差:" << std::setprecision(1) << lastError.Length()
                         << " 输出:" << lastOutput.Length();
            }

            std::cout << "                              " << std::flush;  // 额外空格清除旧内容
        }

        // 渲染ImGui
        ImGui::Render();
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        // 处理多视口
        ImGuiIO& io = ImGui::GetIO();
        if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable) {
            ImGui::UpdatePlatformWindows();
            ImGui::RenderPlatformWindowsDefault();
        }

        // F4键退出
        if (GetAsyncKeyState(VK_F4) & 0x8000) {
            break;
        }
    }

    std::cout << "\n正在关闭自瞄系统..." << std::endl;

    // 清理ImGui
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    // 停止鼠标移动线程
    if (mouseMoveThread) {
        mouseMoveThread->Stop();
    }

    mouseController->Disconnect();
    std::cout << "自瞄系统已关闭" << std::endl;
    return true;
}

int main() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "此应用程序捕获屏幕中心320x320区域" << std::endl;
    std::cout << "并运行AI推理来实时检测头部和身体目标，实现智能自瞄。" << std::endl;
    std::cout << std::endl;

    bool success = RunAimBotSystem();

    std::cout << std::endl;
    if (success) {
        std::cout << "应用程序成功完成！" << std::endl;
    } else {
        std::cout << "应用程序运行失败！" << std::endl;
    }

    std::cout << "按Enter键退出..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
