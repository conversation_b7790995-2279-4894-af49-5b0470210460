#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>

// 核心项目头文件
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"
#include "mouse_controller.h"
#include "pid_controller.h"

using Microsoft::WRL::ComPtr;

// 删除了RunSimpleScreenCapture和RunRealtimeInference函数

// 自瞄系统 - 完整的FPS游戏自瞄程序
bool RunAimBotSystem() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "测试流程: 屏幕捕获 -> GPU预处理 -> AI推理 -> 目标选择 -> PID控制 -> 鼠标移动" << std::endl;
    std::cout << "控制说明: F1-启用自瞄 | F2-自动开火 | 右键-激活瞄准 | ESC-退出" << std::endl;

    // 初始化屏幕捕获
    std::cout << "正在初始化屏幕捕获..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "屏幕捕获初始化失败" << std::endl;
        return false;
    }
    std::cout << "屏幕捕获初始化成功" << std::endl;

    // 初始化GPU预处理器
    std::cout << "正在初始化GPU预处理器..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);
    params.normalizationScale = 1.0f;
    params.meanR = 0.0f;
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "GPU预处理器初始化失败" << std::endl;
        return false;
    }

    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU预处理器初始化成功" << std::endl;
    std::cout << "感兴趣区域: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // 初始化推理引擎
    std::cout << "正在初始化推理引擎..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "推理引擎初始化失败" << std::endl;
        return false;
    }
    std::cout << "推理引擎初始化成功" << std::endl;

    // 初始化GPU渲染器（可选显示）
    std::cout << "正在初始化GPU渲染器..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;
    renderConfig.showBoundingBoxes = true;

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "GPU渲染器初始化失败" << std::endl;
        return false;
    }
    std::cout << "GPU渲染器初始化成功" << std::endl;

    // 初始化显示窗口（可选）
    std::cout << "正在初始化显示窗口..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "显示窗口初始化失败" << std::endl;
        return false;
    }
    std::cout << "显示窗口初始化成功" << std::endl;

    // 初始化鼠标控制器
    std::cout << "正在初始化鼠标控制器..." << std::endl;
    auto mouseController = MouseControllerFactory::Create("kmbox");
    if (!mouseController) {
        std::cout << "鼠标控制器创建失败" << std::endl;
        return false;
    }

    std::string mouseConfig = R"({
        "ip": "*************",
        "port": "8808",
        "mac": "62587019",
        "enableEncryption": true
    })";

    if (!mouseController->Initialize(mouseConfig)) {
        std::cout << "鼠标控制器初始化失败: " << mouseController->GetLastErrorMessage() << std::endl;
        return false;
    }
    std::cout << "鼠标控制器初始化成功: " << mouseController->GetControllerType() << std::endl;

    // 初始化PID控制器
    std::cout << "正在初始化PID控制器..." << std::endl;
    auto pidController = std::make_unique<PIDController>(
        PIDControllerFactory::CreateAdaptiveResponse()
    );

    // 设置目标到达回调（自动开火）
    bool autoFireEnabled = false;
    pidController->SetTargetReachedCallback([&mouseController, &autoFireEnabled]() {
        if (autoFireEnabled) {
            mouseController->ClickMouseButton(MouseController::MouseButton::Left, 50);
            std::cout << "🎯 自动开火！" << std::endl;
        }
    });

    std::cout << "PID控制器初始化成功" << std::endl;

    // 自瞄系统配置
    bool aimEnabled = false;
    Vector2D screenCenter(160.0f, 160.0f);  // 320x320屏幕的中心
    float aimFOV = 80.0f;                   // 自瞄视野范围
    float headPriority = 1.5f;              // 头部优先级倍数

    std::cout << "\n=== 自瞄系统就绪 ===" << std::endl;
    std::cout << "屏幕中心: (" << screenCenter.x << ", " << screenCenter.y << ")" << std::endl;
    std::cout << "自瞄视野: " << aimFOV << " 像素" << std::endl;
    std::cout << "控制说明:" << std::endl;
    std::cout << "  F1 - 启用/禁用自瞄" << std::endl;
    std::cout << "  F2 - 启用/禁用自动开火" << std::endl;
    std::cout << "  右键 - 激活瞄准（需要按住）" << std::endl;
    std::cout << "  ESC - 退出程序" << std::endl;
    std::cout << "===================" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    // 目标选择函数
    auto SelectBestTarget = [&](const std::vector<Detection>& detections) -> Detection* {
        if (detections.empty()) return nullptr;

        Detection* bestTarget = nullptr;
        float bestScore = -1.0f;

        for (const auto& detection : detections) {
            // 计算目标中心点
            Vector2D targetCenter(detection.x + detection.width / 2.0f,
                                 detection.y + detection.height / 2.0f);

            // 计算距离屏幕中心的距离
            float distance = (targetCenter - screenCenter).Length();

            // 检查是否在自瞄视野范围内
            if (distance > aimFOV) continue;

            // 计算优先级分数
            float score = detection.confidence;

            // 头部目标优先级更高（假设classId=0是头部）
            if (detection.classId == 0) {
                score *= headPriority;
            }

            // 距离越近优先级越高
            score *= (aimFOV - distance) / aimFOV;

            if (score > bestScore) {
                bestScore = score;
                bestTarget = const_cast<Detection*>(&detection);
            }
        }

        return bestTarget;
    };

    std::cout << "\n开始运行自瞄系统..." << std::endl;

    while (!displayWindow.ShouldClose()) {
        // 处理窗口消息
        displayWindow.ProcessMessages();
        frameCount++;

        // 检查用户输入
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            aimEnabled = !aimEnabled;
            std::cout << "自瞄状态: " << (aimEnabled ? "✅ 启用" : "❌ 禁用") << std::endl;
            if (!aimEnabled) pidController->Reset();
            Sleep(200); // 防止重复触发
        }

        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            autoFireEnabled = !autoFireEnabled;
            std::cout << "自动开火: " << (autoFireEnabled ? "✅ 启用" : "❌ 禁用") << std::endl;
            Sleep(200);
        }

        // 高精度计时
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. 捕获屏幕
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. GPU预处理
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. AI推理（GPU上进行，包含NMS后处理）
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. 自瞄逻辑（CPU上进行）
        QueryPerformanceCounter(&start);
        double aimTime = 0.0;

        if (aimEnabled && mouseController->IsConnected()) {
            // 检查右键是否按下（激活瞄准）
            if (mouseController->IsMouseButtonPressed(MouseController::MouseButton::Right)) {
                // 选择最佳目标
                Detection* bestTarget = SelectBestTarget(detections);

                if (bestTarget) {
                    // 计算目标中心点
                    Vector2D targetCenter(bestTarget->x + bestTarget->width / 2.0f,
                                         bestTarget->y + bestTarget->height / 2.0f);

                    // 计算误差（目标位置 - 准星位置）
                    Vector2D error = targetCenter - screenCenter;

                    // 使用PID控制器计算移动向量
                    Vector2D moveVector = pidController->Calculate(error);

                    // 执行鼠标移动
                    if (moveVector.Length() > 0.5f) {
                        mouseController->MoveMouse(
                            static_cast<int>(moveVector.x),
                            static_cast<int>(moveVector.y),
                            MouseController::MoveType::Smooth,
                            50  // 50ms平滑移动
                        );
                    }

                    // 显示调试信息（每30帧一次）
                    if (frameCount % 30 == 0) {
                        std::cout << "🎯 目标: (" << targetCenter.x << ", " << targetCenter.y
                                 << ") 误差: " << error.Length()
                                 << " 输出: " << moveVector.Length()
                                 << " 类型: " << (bestTarget->classId == 0 ? "头部" : "身体") << std::endl;
                    }
                }
            }
        }

        QueryPerformanceCounter(&end);
        aimTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 5. 渲染显示（可选）
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
            continue;
        }
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + aimTime + displayTime;

        // 每30帧显示性能信息
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            std::cout << "[" << frameCount << "] "
                      << "捕获:" << std::fixed << std::setprecision(2) << captureTime << "ms "
                      << "预处理:" << preprocessTime << "ms "
                      << "推理:" << inferenceTime << "ms "
                      << "自瞄:" << aimTime << "ms "
                      << "显示:" << displayTime << "ms "
                      << "总计:" << totalTime << "ms | "
                      << "帧率: " << std::setprecision(1) << fps << " | "
                      << "检测数: " << detections.size()
                      << " | 自瞄: " << (aimEnabled ? "ON" : "OFF") << std::endl;
        }

        // ESC键退出
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "\n正在关闭自瞄系统..." << std::endl;
    mouseController->Disconnect();
    std::cout << "自瞄系统已关闭" << std::endl;
    return true;
}

int main() {
    std::cout << "=== FPS游戏自瞄系统 ===" << std::endl;
    std::cout << "此应用程序捕获屏幕中心320x320区域" << std::endl;
    std::cout << "并运行AI推理来实时检测头部和身体目标，实现智能自瞄。" << std::endl;
    std::cout << std::endl;

    bool success = RunAimBotSystem();

    std::cout << std::endl;
    if (success) {
        std::cout << "应用程序成功完成！" << std::endl;
    } else {
        std::cout << "应用程序运行失败！" << std::endl;
    }

    std::cout << "按Enter键退出..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
