#include <iostream>
#include <thread>
#include <chrono>
#include <Windows.h>
#include "kmbox_controller.h"
#include "inference_engine.h"

/**
 * @brief KMBoxController集成示例
 * 
 * 这个示例展示了如何将KMBoxController集成到自瞄系统中，
 * 实现从AI检测结果到鼠标控制的完整流程。
 */

// 简单的自瞄逻辑示例
class SimpleAimBot {
private:
    std::shared_ptr<KMBoxController> m_kmboxController;
    bool m_enabled = false;
    float m_sensitivity = 1.0f;
    float m_maxMoveDistance = 100.0f;  // 每次最大移动距离
    
public:
    SimpleAimBot(std::shared_ptr<KMBoxController> controller) 
        : m_kmboxController(controller) {}
    
    void SetEnabled(bool enabled) { m_enabled = enabled; }
    bool IsEnabled() const { return m_enabled; }
    
    void SetSensitivity(float sensitivity) { m_sensitivity = sensitivity; }
    
    /**
     * @brief 处理AI检测结果并执行自瞄
     * @param detections 检测结果
     * @param screenCenterX 屏幕中心X坐标
     * @param screenCenterY 屏幕中心Y坐标
     */
    void ProcessDetections(const std::vector<Detection>& detections, 
                          float screenCenterX, float screenCenterY) {
        if (!m_enabled || !m_kmboxController || !m_kmboxController->IsConnected()) {
            return;
        }
        
        // 找到最近的目标
        Detection bestTarget;
        float minDistance = FLT_MAX;
        bool foundTarget = false;
        
        for (const auto& detection : detections) {
            // 计算目标中心点
            float targetCenterX = detection.x + detection.width / 2.0f;
            float targetCenterY = detection.y + detection.height / 2.0f;
            
            // 计算距离屏幕中心的距离
            float distance = sqrt(pow(targetCenterX - screenCenterX, 2) + 
                                pow(targetCenterY - screenCenterY, 2));
            
            // 选择最近的目标
            if (distance < minDistance && distance < m_maxMoveDistance) {
                minDistance = distance;
                bestTarget = detection;
                foundTarget = true;
            }
        }
        
        if (foundTarget) {
            // 计算目标中心点
            float targetCenterX = bestTarget.x + bestTarget.width / 2.0f;
            float targetCenterY = bestTarget.y + bestTarget.height / 2.0f;
            
            // 计算需要移动的距离
            float deltaX = (targetCenterX - screenCenterX) * m_sensitivity;
            float deltaY = (targetCenterY - screenCenterY) * m_sensitivity;
            
            // 限制移动距离
            float moveDistance = sqrt(deltaX * deltaX + deltaY * deltaY);
            if (moveDistance > m_maxMoveDistance) {
                float scale = m_maxMoveDistance / moveDistance;
                deltaX *= scale;
                deltaY *= scale;
            }
            
            // 执行鼠标移动
            if (abs(deltaX) > 1.0f || abs(deltaY) > 1.0f) {
                m_kmboxController->MoveMouse(static_cast<int>(deltaX), static_cast<int>(deltaY));
                
                std::cout << "瞄准目标: (" << targetCenterX << ", " << targetCenterY 
                         << ") 移动: (" << deltaX << ", " << deltaY << ")" << std::endl;
            }
        }
    }
};

// 模拟AI检测结果的函数
std::vector<Detection> SimulateDetections() {
    std::vector<Detection> detections;
    
    // 模拟一些随机的检测结果
    static int frame = 0;
    frame++;
    
    if (frame % 30 == 0) {  // 每30帧生成一个新目标
        Detection detection;
        detection.x = 100 + (rand() % 120);  // 在屏幕中心附近随机位置
        detection.y = 100 + (rand() % 120);
        detection.width = 20 + (rand() % 20);
        detection.height = 30 + (rand() % 20);
        detection.confidence = 0.7f + (rand() % 30) / 100.0f;
        detection.classId = 0;  // 假设0是头部
        
        detections.push_back(detection);
        
        std::cout << "模拟检测到目标: (" << detection.x << ", " << detection.y 
                 << ") 大小: " << detection.width << "x" << detection.height 
                 << " 置信度: " << detection.confidence << std::endl;
    }
    
    return detections;
}

void PrintUsageInstructions() {
    std::cout << "\n=== KMBoxController 集成示例 ===" << std::endl;
    std::cout << "这个示例展示了如何将KMBoxController集成到自瞄系统中" << std::endl;
    std::cout << "\n控制说明:" << std::endl;
    std::cout << "F1 - 启用/禁用自瞄" << std::endl;
    std::cout << "F2 - 增加灵敏度" << std::endl;
    std::cout << "F3 - 减少灵敏度" << std::endl;
    std::cout << "F4 - 测试鼠标移动" << std::endl;
    std::cout << "ESC - 退出程序" << std::endl;
    std::cout << "\n程序将模拟AI检测结果并自动瞄准目标" << std::endl;
    std::cout << "请确保KMBoxNet设备已正确连接\n" << std::endl;
}

int main() {
    std::cout << "=== KMBoxController 集成示例程序 ===" << std::endl;
    
    // 创建KMBoxController实例
    auto kmboxController = std::make_shared<KMBoxController>();
    
    // 设置错误回调
    kmboxController->SetErrorCallback([](int errorCode, const std::string& errorMessage) {
        std::cout << "KMBox错误: [" << errorCode << "] " << errorMessage << std::endl;
    });
    
    // 连接设备
    KMBoxController::DeviceConfig config;
    std::cout << "正在连接KMBoxNet设备..." << std::endl;
    std::cout << "IP: " << config.ip << ", Port: " << config.port << ", MAC: " << config.mac << std::endl;
    
    if (!kmboxController->Connect(config)) {
        std::cout << "❌ 设备连接失败: " << kmboxController->GetLastErrorMessage() << std::endl;
        std::cout << "请检查设备连接和配置，然后重试" << std::endl;
        std::cout << "按Enter键退出...";
        std::cin.get();
        return -1;
    }
    
    std::cout << "✅ KMBoxNet设备连接成功！" << std::endl;
    
    // 创建简单自瞄机器人
    SimpleAimBot aimBot(kmboxController);
    
    // 显示使用说明
    PrintUsageInstructions();
    
    // 主循环
    bool running = true;
    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::cout << "开始运行自瞄系统..." << std::endl;
    
    while (running) {
        frameCount++;
        
        // 检查按键
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            running = false;
            break;
        }
        
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            aimBot.SetEnabled(!aimBot.IsEnabled());
            std::cout << "自瞄状态: " << (aimBot.IsEnabled() ? "启用" : "禁用") << std::endl;
            Sleep(200); // 防止重复触发
        }
        
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            aimBot.SetSensitivity(std::min(2.0f, aimBot.IsEnabled() ? 1.1f : 1.0f));
            std::cout << "灵敏度增加" << std::endl;
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            aimBot.SetSensitivity(std::max(0.1f, aimBot.IsEnabled() ? 0.9f : 1.0f));
            std::cout << "灵敏度减少" << std::endl;
            Sleep(200);
        }
        
        if (GetAsyncKeyState(VK_F4) & 0x8000) {
            std::cout << "测试鼠标移动..." << std::endl;
            kmboxController->MoveMouse(10, 0);
            Sleep(100);
            kmboxController->MoveMouse(-10, 0);
            Sleep(200);
        }
        
        // 模拟AI检测结果
        auto detections = SimulateDetections();
        
        // 处理检测结果（假设屏幕中心为160, 160）
        aimBot.ProcessDetections(detections, 160.0f, 160.0f);
        
        // 显示状态信息（每秒一次）
        if (frameCount % 60 == 0) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime);
            
            std::cout << "\n=== 状态信息 ===" << std::endl;
            std::cout << "运行时间: " << duration.count() << "秒" << std::endl;
            std::cout << "处理帧数: " << frameCount << std::endl;
            std::cout << "自瞄状态: " << (aimBot.IsEnabled() ? "启用" : "禁用") << std::endl;
            std::cout << "设备连接: " << (kmboxController->IsConnected() ? "正常" : "断开") << std::endl;
            std::cout << "检测目标: " << detections.size() << "个" << std::endl;
            std::cout << "================\n" << std::endl;
        }
        
        // 控制帧率（约60FPS）
        Sleep(16);
    }
    
    std::cout << "\n正在退出程序..." << std::endl;
    
    // 禁用自瞄
    aimBot.SetEnabled(false);
    
    // 断开设备连接
    kmboxController->Disconnect();
    
    std::cout << "程序已退出" << std::endl;
    std::cout << "按Enter键关闭窗口...";
    std::cin.get();
    
    return 0;
}
