#include "kmbox_controller.h"
#include "kmbox/kmboxNet.h"
#include <iostream>
#include <chrono>
#include <cmath>
#include <Windows.h>

// 错误码定义（来自kmboxNet.h）
extern const int err_creat_socket;
extern const int err_net_version;
extern const int err_net_tx;
extern const int err_net_rx_timeout;
extern const int err_net_cmd;
extern const int err_net_pts;

KMBoxController::KMBoxController()
    : m_connectionStatus(ConnectionStatus::Disconnected)
    , m_monitoring(false)
    , m_lastError(0)
    , m_lastErrorMessage("")
{
}

KMBoxController::~KMBoxController() {
    Disconnect();
}

bool KMBoxController::Connect(const DeviceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_config = config;
    m_connectionStatus = ConnectionStatus::Connecting;
    
    std::cout << "正在连接KMBoxNet设备..." << std::endl;
    std::cout << "IP: " << config.ip << ", Port: " << config.port << ", MAC: " << config.mac << std::endl;
    
    return ConnectWithRetry();
}

void KMBoxController::Disconnect() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 停止监控
    StopMonitoring();
    
    m_connectionStatus = ConnectionStatus::Disconnected;
    std::cout << "KMBoxNet设备已断开连接" << std::endl;
}

KMBoxController::ConnectionStatus KMBoxController::GetConnectionStatus() const {
    return m_connectionStatus.load();
}

bool KMBoxController::IsConnected() const {
    return m_connectionStatus.load() == ConnectionStatus::Connected;
}

// ==================== 鼠标移动控制 ====================

bool KMBoxController::MoveMouse(int deltaX, int deltaY) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [deltaX, deltaY]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move(static_cast<short>(deltaX), static_cast<short>(deltaY));
        } else {
            return kmNet_mouse_move(static_cast<short>(deltaX), static_cast<short>(deltaY));
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::SmoothMoveMouse(int deltaX, int deltaY, int durationMs) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [deltaX, deltaY, durationMs]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move_auto(deltaX, deltaY, durationMs);
        } else {
            return kmNet_mouse_move_auto(deltaX, deltaY, durationMs);
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::BezierMoveMouse(int deltaX, int deltaY, int durationMs, 
                                     int controlX1, int controlY1, int controlX2, int controlY2) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [=]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move_beizer(deltaX, deltaY, durationMs, controlX1, controlY1, controlX2, controlY2);
        } else {
            return kmNet_mouse_move_beizer(deltaX, deltaY, durationMs, controlX1, controlY1, controlX2, controlY2);
        }
    };
    
    return ExecuteWithRetry(operation);
}

// ==================== 鼠标按键控制 ====================

bool KMBoxController::PressMouseButton(MouseButton button) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [this, button]() -> int {
        switch (button) {
            case MouseButton::Left:
                return m_config.enableEncryption ? kmNet_enc_mouse_left(1) : kmNet_mouse_left(1);
            case MouseButton::Right:
                return m_config.enableEncryption ? kmNet_enc_mouse_right(1) : kmNet_mouse_right(1);
            case MouseButton::Middle:
                return m_config.enableEncryption ? kmNet_enc_mouse_middle(1) : kmNet_mouse_middle(1);
            case MouseButton::Side1:
                return m_config.enableEncryption ? kmNet_enc_mouse_side1(1) : kmNet_mouse_side1(1);
            case MouseButton::Side2:
                return m_config.enableEncryption ? kmNet_enc_mouse_side2(1) : kmNet_mouse_side2(1);
            default:
                return -1;
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::ReleaseMouseButton(MouseButton button) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [this, button]() -> int {
        switch (button) {
            case MouseButton::Left:
                return m_config.enableEncryption ? kmNet_enc_mouse_left(0) : kmNet_mouse_left(0);
            case MouseButton::Right:
                return m_config.enableEncryption ? kmNet_enc_mouse_right(0) : kmNet_mouse_right(0);
            case MouseButton::Middle:
                return m_config.enableEncryption ? kmNet_enc_mouse_middle(0) : kmNet_mouse_middle(0);
            case MouseButton::Side1:
                return m_config.enableEncryption ? kmNet_enc_mouse_side1(0) : kmNet_mouse_side1(0);
            case MouseButton::Side2:
                return m_config.enableEncryption ? kmNet_enc_mouse_side2(0) : kmNet_mouse_side2(0);
            default:
                return -1;
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::ClickMouseButton(MouseButton button, int holdTimeMs) {
    if (!PressMouseButton(button)) {
        return false;
    }
    
    Sleep(holdTimeMs);
    
    return ReleaseMouseButton(button);
}

bool KMBoxController::ScrollWheel(int direction, int steps) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    int wheelValue = (direction > 0) ? 3 : -3;
    
    for (int i = 0; i < steps; i++) {
        auto operation = [this, wheelValue]() -> int {
            return m_config.enableEncryption ? kmNet_enc_mouse_wheel(wheelValue) : kmNet_mouse_wheel(wheelValue);
        };
        
        if (!ExecuteWithRetry(operation)) {
            return false;
        }
        
        Sleep(50); // 短暂延迟避免过快滚动
    }
    
    return true;
}

// ==================== 私有方法实现 ====================

bool KMBoxController::ConnectWithRetry() {
    for (int attempt = 0; attempt < m_config.maxRetries; attempt++) {
        std::cout << "连接尝试 " << (attempt + 1) << "/" << m_config.maxRetries << std::endl;
        
        int result = kmNet_init(
            const_cast<char*>(m_config.ip.c_str()),
            const_cast<char*>(m_config.port.c_str()),
            const_cast<char*>(m_config.mac.c_str())
        );
        
        if (result == 0) {
            m_connectionStatus = ConnectionStatus::Connected;
            m_lastError = 0;
            m_lastErrorMessage = "";
            std::cout << "KMBoxNet连接成功！" << std::endl;
            return true;
        }
        
        std::string errorMsg = "连接失败，错误码: " + std::to_string(result) + " - " + GetErrorMessage(result);
        std::cout << errorMsg << std::endl;
        
        if (attempt < m_config.maxRetries - 1) {
            std::cout << "等待 " << m_config.retryDelayMs << "ms 后重试..." << std::endl;
            Sleep(m_config.retryDelayMs);
        }
    }
    
    m_connectionStatus = ConnectionStatus::Error;
    HandleError(-1, "连接失败，已达到最大重试次数");
    return false;
}

void KMBoxController::HandleError(int errorCode, const std::string& message) {
    m_lastError = errorCode;
    m_lastErrorMessage = message;
    
    if (m_errorCallback) {
        m_errorCallback(errorCode, message);
    }
    
    std::cerr << "KMBoxController错误: " << message << " (错误码: " << errorCode << ")" << std::endl;
}

bool KMBoxController::ExecuteWithRetry(std::function<int()> operation, int maxRetries) {
    for (int attempt = 0; attempt < maxRetries; attempt++) {
        int result = operation();
        
        if (result == 0) {
            return true;
        }
        
        // 如果是网络错误，尝试重连
        if (result == err_net_tx || result == err_net_rx_timeout) {
            std::cout << "网络错误，尝试重连..." << std::endl;
            if (ConnectWithRetry()) {
                continue; // 重连成功，重试操作
            }
        }
        
        HandleError(result, GetErrorMessage(result));
    }
    
    return false;
}

std::string KMBoxController::GetErrorMessage(int errorCode) const {
    switch (errorCode) {
        case 0: return "成功";
        case err_creat_socket: return "创建socket失败";
        case err_net_version: return "socket版本错误";
        case err_net_tx: return "socket发送错误";
        case err_net_rx_timeout: return "socket接收超时";
        case err_net_cmd: return "命令错误";
        case err_net_pts: return "时间戳错误";
        default: return "未知错误";
    }
}
