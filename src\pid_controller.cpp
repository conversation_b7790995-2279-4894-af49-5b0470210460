#include "pid_controller.h"
#include <iostream>
#include <sstream>
#include <cmath>

PIDController::PIDController(const PIDConfig& config)
    : m_config(config)
    , m_lastError(0.0f, 0.0f)
    , m_integral(0.0f, 0.0f)
    , m_derivative(0.0f, 0.0f)
    , m_filteredDerivative(0.0f, 0.0f)
    , m_firstRun(true)
{
    Reset();
}

Vector2D PIDController::Calculate(const Vector2D& error) {
    // 计算时间间隔
    float deltaTime = CalculateDeltaTime();
    
    // 应用死区
    Vector2D effectiveError = ApplyDeadZone(error);
    
    // 如果在死区内，返回零向量
    if (effectiveError.Length() < 0.001f) {
        m_debugInfo.inDeadZone = true;
        if (m_targetReachedCallback) {
            m_targetReachedCallback();
        }
        return Vector2D(0.0f, 0.0f);
    }
    
    m_debugInfo.inDeadZone = false;
    
    // 计算P项（比例项）
    Vector2D pOutput = effectiveError * m_config.kp;
    
    // 更新并计算I项（积分项）
    UpdateIntegral(effectiveError, deltaTime);
    Vector2D iOutput = m_integral * m_config.ki;
    
    // 更新并计算D项（微分项）
    UpdateDerivative(effectiveError, deltaTime);
    Vector2D dOutput = m_derivative * m_config.kd;
    
    // 计算总输出
    Vector2D totalOutput = pOutput + iOutput + dOutput;
    
    // 应用自适应增益
    if (m_config.enableAdaptiveGain) {
        totalOutput = ApplyAdaptiveGain(totalOutput, effectiveError);
    }
    
    // 应用输出限制
    totalOutput = ApplyLimits(totalOutput);
    
    // 更新调试信息
    UpdateDebugInfo(effectiveError, pOutput, iOutput, dOutput, totalOutput, deltaTime);
    
    // 更新状态
    m_lastError = effectiveError;
    
    return totalOutput;
}

void PIDController::Reset() {
    m_lastError = Vector2D(0.0f, 0.0f);
    m_integral = Vector2D(0.0f, 0.0f);
    m_derivative = Vector2D(0.0f, 0.0f);
    m_filteredDerivative = Vector2D(0.0f, 0.0f);
    m_firstRun = true;
    m_lastTime = std::chrono::high_resolution_clock::now();
    
    std::cout << "PID控制器已重置" << std::endl;
}

void PIDController::UpdateConfig(const PIDConfig& config) {
    m_config = config;
    std::cout << "PID配置已更新: Kp=" << config.kp 
              << ", Ki=" << config.ki 
              << ", Kd=" << config.kd << std::endl;
}

PIDController::PIDConfig PIDController::GetConfig() const {
    return m_config;
}

PIDController::DebugInfo PIDController::GetDebugInfo() const {
    return m_debugInfo;
}

void PIDController::SetTargetReachedCallback(std::function<void()> callback) {
    m_targetReachedCallback = callback;
}

// ==================== 私有方法实现 ====================

float PIDController::CalculateDeltaTime() {
    auto currentTime = std::chrono::high_resolution_clock::now();
    
    if (m_firstRun) {
        m_firstRun = false;
        m_lastTime = currentTime;
        return 0.016f; // 假设60FPS，返回16ms
    }
    
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(currentTime - m_lastTime);
    float deltaTime = duration.count() / 1000000.0f; // 转换为秒
    
    // 限制deltaTime范围，避免异常值
    deltaTime = std::max(0.001f, std::min(0.1f, deltaTime));
    
    m_lastTime = currentTime;
    return deltaTime;
}

Vector2D PIDController::ApplyLimits(const Vector2D& output) {
    Vector2D limitedOutput = output;
    
    // 计算输出向量的长度
    float outputLength = output.Length();
    
    if (outputLength > m_config.maxOutput) {
        // 如果超过最大输出，按比例缩放
        float scale = m_config.maxOutput / outputLength;
        limitedOutput = output * scale;
    } else if (outputLength < m_config.minOutput && outputLength > 0.001f) {
        // 如果小于最小输出但不为零，放大到最小输出
        float scale = m_config.minOutput / outputLength;
        limitedOutput = output * scale;
    }
    
    return limitedOutput;
}

Vector2D PIDController::ApplyDeadZone(const Vector2D& error) {
    float errorLength = error.Length();
    
    if (errorLength <= m_config.deadZone) {
        return Vector2D(0.0f, 0.0f);
    }
    
    // 线性缩放，使死区外的误差平滑过渡
    float scale = (errorLength - m_config.deadZone) / errorLength;
    return error * scale;
}

Vector2D PIDController::ApplyAdaptiveGain(const Vector2D& output, const Vector2D& error) {
    float errorLength = error.Length();
    
    if (errorLength > m_config.adaptiveThreshold) {
        // 大误差时增加增益
        float adaptiveFactor = 1.0f + (errorLength / m_config.adaptiveThreshold - 1.0f) * m_config.adaptiveGainFactor;
        return output * adaptiveFactor;
    }
    
    return output;
}

void PIDController::UpdateIntegral(const Vector2D& error, float deltaTime) {
    // 累积积分
    m_integral = m_integral + error * deltaTime;
    
    // 积分限幅
    float integralLength = m_integral.Length();
    if (integralLength > m_config.maxIntegral) {
        float scale = m_config.maxIntegral / integralLength;
        m_integral = m_integral * scale;
    }
    
    // 积分衰减（防止积分饱和）
    if (m_config.enableIntegralDecay) {
        m_integral = m_integral * m_config.integralDecayRate;
    }
}

void PIDController::UpdateDerivative(const Vector2D& error, float deltaTime) {
    if (deltaTime > 0.0f) {
        // 计算微分
        m_derivative = (error - m_lastError) * (1.0f / deltaTime);
        
        // 微分滤波（减少噪声）
        if (m_config.enableDerivativeFilter) {
            float alpha = m_config.derivativeFilterAlpha;
            m_filteredDerivative = m_filteredDerivative * alpha + m_derivative * (1.0f - alpha);
            m_derivative = m_filteredDerivative;
        }
    }
}

void PIDController::UpdateDebugInfo(const Vector2D& error, const Vector2D& pOut, 
                                   const Vector2D& iOut, const Vector2D& dOut, 
                                   const Vector2D& totalOut, float deltaTime) {
    m_debugInfo.lastError = error;
    m_debugInfo.integral = m_integral;
    m_debugInfo.derivative = m_derivative;
    m_debugInfo.pOutput = pOut;
    m_debugInfo.iOutput = iOut;
    m_debugInfo.dOutput = dOut;
    m_debugInfo.totalOutput = totalOut;
    m_debugInfo.deltaTime = deltaTime;
}

// ==================== 工厂方法实现 ====================

PIDController PIDControllerFactory::CreateFastResponse() {
    PIDController::PIDConfig config;
    config.kp = 1.2f;           // 高比例增益，快速响应
    config.ki = 0.0f;           // 中等积分增益
    config.kd = 0.0f;           // 低微分增益，减少震荡
    config.maxOutput = 100.0f;   // 较高的最大输出
    config.deadZone = 0.0f;     // 较小的死区
    
    std::cout << "创建快速响应PID控制器" << std::endl;
    return PIDController(config);
}

PIDController PIDControllerFactory::CreateSmoothResponse() {
    PIDController::PIDConfig config;
    config.kp = 0.6f;           // 中等比例增益
    config.ki = 0.05f;          // 低积分增益
    config.kd = 0.3f;           // 高微分增益，平滑响应
    config.maxOutput = 50.0f;   // 中等最大输出
    config.deadZone = 3.0f;     // 较大的死区
    config.enableDerivativeFilter = true;
    config.derivativeFilterAlpha = 0.8f;
    
    std::cout << "创建平滑响应PID控制器" << std::endl;
    return PIDController(config);
}

PIDController PIDControllerFactory::CreatePreciseResponse() {
    PIDController::PIDConfig config;
    config.kp = 0.8f;           // 中等比例增益
    config.ki = 0.15f;          // 中等积分增益，消除稳态误差
    config.kd = 0.25f;          // 中等微分增益
    config.maxOutput = 30.0f;   // 较低的最大输出，精确控制
    config.minOutput = 0.5f;    // 较低的最小输出
    config.deadZone = 1.0f;     // 小死区，精确到位
    config.enableIntegralDecay = true;
    
    std::cout << "创建精确响应PID控制器" << std::endl;
    return PIDController(config);
}

PIDController PIDControllerFactory::CreateAdaptiveResponse() {
    PIDController::PIDConfig config;
    config.kp = 0.8f;
    config.ki = 0.1f;
    config.kd = 0.2f;
    config.maxOutput = 100.0f;
    config.deadZone = 2.0f;
    config.enableAdaptiveGain = true;
    config.adaptiveGainFactor = 1.5f;
    config.adaptiveThreshold = 20.0f;
    config.enableIntegralDecay = true;
    config.enableDerivativeFilter = true;
    
    std::cout << "创建自适应响应PID控制器" << std::endl;
    return PIDController(config);
}

PIDController PIDControllerFactory::CreateFromString(const std::string& configStr) {
    PIDController::PIDConfig config;
    
    std::istringstream iss(configStr);
    std::string token;
    
    // 解析格式："kp,ki,kd" 或 "kp,ki,kd,maxOutput"
    if (std::getline(iss, token, ',')) {
        config.kp = std::stof(token);
    }
    if (std::getline(iss, token, ',')) {
        config.ki = std::stof(token);
    }
    if (std::getline(iss, token, ',')) {
        config.kd = std::stof(token);
    }
    if (std::getline(iss, token, ',')) {
        config.maxOutput = std::stof(token);
    }
    
    std::cout << "从字符串创建PID控制器: " << configStr << std::endl;
    return PIDController(config);
}
