{"artifacts": [{"path": "Debug/AimBot.exe"}, {"path": "Debug/AimBot.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 59, "parent": 0}, {"command": 1, "file": 0, "line": 29, "parent": 0}, {"command": 2, "file": 0, "line": 62, "parent": 0}, {"command": 3, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd"}, {"fragment": "-std:c++17"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Desktop/aim_bot/include"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/aim_bot/NetConfig"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/aim_bot/src/kmbox"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/include"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/build/native/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "AimBot::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\aim_bot\\packages\\Microsoft.AI.DirectML.1.15.4\\bin\\x64-win", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Desktop\\aim_bot\\packages\\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\\runtimes\\win-x64\\native", "role": "libraryPath"}, {"backtrace": 3, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "d3dcompiler.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "DirectML.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "onnxruntime.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "winmm.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "AimBot", "nameOnDisk": "AimBot.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/screen_capture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/gpu_preprocessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/gpu_renderer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/inference_engine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/pid_controller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mouse_controller_factory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/kmbox_controller_new.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/kmbox/kmboxNet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/kmbox/my_enc.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/imgui_pid_debugger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mouse_move_thread.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui_draw.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui_tables.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui_widgets.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui_impl_dx11.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "imgui/imgui_impl_win32.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}