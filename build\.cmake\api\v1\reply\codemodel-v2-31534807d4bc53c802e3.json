{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-da67e4f18a97ea16e43b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Debug-92ab7f01c603874694cc.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Debug-a9119cc6519242bd447a.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Debug-231ce62a78a515b90baa.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-da67e4f18a97ea16e43b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Release-5d074bcd5197a9c6b1a3.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Release-3d268d7893efc691812d.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Release-f3dc85e0db94a32090ff.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-da67e4f18a97ea16e43b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-MinSizeRel-17a1db2160940fca3c8b.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-MinSizeRel-6ac2a74cbd72b9b14bcc.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-MinSizeRel-fd002562101848383454.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-da67e4f18a97ea16e43b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-RelWithDebInfo-60261de22f576cbb4fa3.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-RelWithDebInfo-33c699b8435de4a8945c.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-RelWithDebInfo-3a03a6f7b18197c7bb8a.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/aim_bot/build", "source": "C:/Users/<USER>/Desktop/aim_bot"}, "version": {"major": 2, "minor": 4}}