﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B23D0809-0F0A-3323-84F5-A2A2B11040B9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>AimBot</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\aim_bot\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AimBot.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AimBot</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\aim_bot\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AimBot.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AimBot</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\aim_bot\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AimBot.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AimBot</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\aim_bot\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AimBot.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AimBot</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/aim_bot/build/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/aim_bot/build/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/aim_bot/build/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>d3d11.lib;dxgi.lib;d3dcompiler.lib;DirectML.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/$(Configuration);C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/aim_bot/build/Debug/AimBot.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/aim_bot/build/Debug/AimBot.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/aim_bot/build/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/aim_bot/build/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/aim_bot/build/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>d3d11.lib;dxgi.lib;d3dcompiler.lib;DirectML.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/$(Configuration);C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/aim_bot/build/Release/AimBot.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/aim_bot/build/Release/AimBot.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/aim_bot/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/aim_bot/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/aim_bot/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>d3d11.lib;dxgi.lib;d3dcompiler.lib;DirectML.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/$(Configuration);C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/aim_bot/build/MinSizeRel/AimBot.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/aim_bot/build/MinSizeRel/AimBot.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\aim_bot\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\aim_bot\packages\Microsoft.ML.OnnxRuntime.DirectML.1.22.1\build\native\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/aim_bot/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/aim_bot/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/aim_bot/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>d3d11.lib;dxgi.lib;d3dcompiler.lib;DirectML.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.AI.DirectML.1.15.4/bin/x64-win/$(Configuration);C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native;C:/Users/<USER>/Desktop/aim_bot/packages/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/aim_bot/build/RelWithDebInfo/AimBot.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/aim_bot/build/RelWithDebInfo/AimBot.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\aim_bot\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/aim_bot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/aim_bot -BC:/Users/<USER>/Desktop/aim_bot/build --check-stamp-file C:/Users/<USER>/Desktop/aim_bot/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/aim_bot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/aim_bot -BC:/Users/<USER>/Desktop/aim_bot/build --check-stamp-file C:/Users/<USER>/Desktop/aim_bot/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/aim_bot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/aim_bot -BC:/Users/<USER>/Desktop/aim_bot/build --check-stamp-file C:/Users/<USER>/Desktop/aim_bot/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/aim_bot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/aim_bot -BC:/Users/<USER>/Desktop/aim_bot/build --check-stamp-file C:/Users/<USER>/Desktop/aim_bot/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\aim_bot\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\aim_bot\src\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\aim_bot\src\screen_capture.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\aim_bot\src\gpu_preprocessor.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\aim_bot\src\gpu_renderer.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\aim_bot\src\inference_engine.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\aim_bot\build\ZERO_CHECK.vcxproj">
      <Project>{37DD16B2-71CF-3686-A90B-C9981F011D39}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>