#pragma once

#include "pid_controller.h"
#include <d3d11.h>
#include <memory>
#include <windows.h>

// ImGuiͷ�ļ�
#include "../imgui/imgui.h"
#include "../imgui/imgui_impl_dx11.h"
#include "../imgui/imgui_impl_win32.h"

/**
 * @brief ImGui PID����������
 *
 * �ṩʵʱ����PID������ͼ�λ�����
 */
class PIDDebugger {
public:
    PIDDebugger();
    ~PIDDebugger();

    /**
     * @brief ��ʼ��ImGui������
     * @param device D3D11�豸
     * @param deviceContext D3D11�豸������
     * @param hwnd ���ھ��
     * @return �Ƿ��ʼ���ɹ�
     */
    bool Initialize(ID3D11Device* device, ID3D11DeviceContext* deviceContext, HWND hwnd);

    /**
     * @brief ����PID������
     * @param pidController PID������ָ��
     */
    void SetPIDController(PIDController* pidController);

    /**
     * @brief ��ʼ�µ�ImGui֡
     */
    void NewFrame();

    /**
     * @brief ��ȾPID���Խ���
     * @param isVisible �����Ƿ�ɼ�
     * @return �����Ƿ��б仯
     */
    bool RenderDebugWindow(bool& isVisible);

    /**
     * @brief ����ImGui֡����Ⱦ
     */
    void EndFrame();

    /**
     * @brief ����Windows��Ϣ
     */
    bool ProcessMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);

    /**
     * @brief ������Դ
     */
    void Shutdown();

private:
    ID3D11Device* m_device = nullptr;
    ID3D11DeviceContext* m_deviceContext = nullptr;
    HWND m_hwnd = nullptr;
    PIDController* m_pidController = nullptr;
    bool m_initialized = false;

    // PID��������
    PIDController::PIDConfig m_configCache;
    bool m_configChanged = false;

    // Tabö��
    enum class TabType {
        BasicPID = 0,
        Advanced,
        Adaptive,
        Debug
    };

    // ����״̬
    TabType m_currentTab = TabType::BasicPID;
    int m_colorTheme = 2; // Ĭ����ɫ����

    // �ⲿ��������main.cpp���룩
    float* m_yOffsetRatioHead = nullptr;
    float* m_yOffsetRatioBody = nullptr;

    /**
     * @brief ��Ⱦ����PID����
     */
    void RenderBasicParams();

    /**
     * @brief ��Ⱦ�߼�����
     */
    void RenderAdvancedParams();

    /**
     * @brief ��Ⱦ����Ӧ����
     */
    void RenderAdaptiveParams();

    /**
     * @brief ��Ⱦ������Ϣ
     */
    void RenderDebugInfo();

    /**
     * @brief ��Ⱦ������
     */
    void RenderSidePanel();



    /**
     * @brief Ӧ�����ø���
     */
    void ApplyConfigChanges();

    /**
     * @brief ����ImGui��ʽ
     */
    void SetupImGuiStyle();

    /**
     * @brief ������ǣ��ο���Ĵ��룩
     */
    void HelpMarker(const char* text, ImVec4 color);

public:
    /**
     * @brief �����ⲿ��������
     */
    void SetExternalParams(float* yOffsetRatioHead, float* yOffsetRatioBody);
};
