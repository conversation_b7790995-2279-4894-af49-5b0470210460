#pragma once

#include "pid_controller.h"
#include <d3d11.h>
#include <memory>
#include <windows.h>

/**
 * @brief PID参数调试器
 *
 * 提供实时调整PID参数的简化图形界面
 * 使用Windows原生控件实现，无需ImGui依赖
 */
class PIDDebugger {
public:
    PIDDebugger();
    ~PIDDebugger();

    /**
     * @brief 初始化调试器
     * @param parentHwnd 父窗口句柄
     * @return 是否初始化成功
     */
    bool Initialize(HWND parentHwnd);

    /**
     * @brief 显示/隐藏调试窗口
     * @param show 是否显示
     */
    void Show(bool show);

    /**
     * @brief 更新PID控制器引用
     * @param pidController PID控制器指针
     */
    void SetPIDController(PIDController* pidController);

    /**
     * @brief 处理Windows消息
     * @param hwnd 窗口句柄
     * @param msg 消息类型
     * @param wParam 消息参数
     * @param lParam 消息参数
     * @return 是否处理了消息
     */
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);

    /**
     * @brief 获取窗口句柄
     */
    HWND GetWindowHandle() const { return m_hwnd; }

    /**
     * @brief 清理资源
     */
    void Shutdown();

private:
    HWND m_hwnd = nullptr;
    HWND m_parentHwnd = nullptr;
    PIDController* m_pidController = nullptr;
    bool m_initialized = false;

    // 控件句柄
    HWND m_kpEdit = nullptr;
    HWND m_kiEdit = nullptr;
    HWND m_kdEdit = nullptr;
    HWND m_maxOutputEdit = nullptr;
    HWND m_deadZoneEdit = nullptr;
    HWND m_yOffsetHeadEdit = nullptr;
    HWND m_yOffsetBodyEdit = nullptr;

    // 按钮句柄
    HWND m_applyBtn = nullptr;
    HWND m_resetBtn = nullptr;
    HWND m_presetFastBtn = nullptr;
    HWND m_presetStableBtn = nullptr;
    HWND m_presetPreciseBtn = nullptr;

    // 静态文本句柄
    HWND m_debugInfoText = nullptr;

    // 控件ID
    enum ControlIDs {
        ID_KP_EDIT = 1001,
        ID_KI_EDIT = 1002,
        ID_KD_EDIT = 1003,
        ID_MAX_OUTPUT_EDIT = 1004,
        ID_DEAD_ZONE_EDIT = 1005,
        ID_Y_OFFSET_HEAD_EDIT = 1006,
        ID_Y_OFFSET_BODY_EDIT = 1007,
        ID_APPLY_BTN = 1010,
        ID_RESET_BTN = 1011,
        ID_PRESET_FAST_BTN = 1012,
        ID_PRESET_STABLE_BTN = 1013,
        ID_PRESET_PRECISE_BTN = 1014
    };

    /**
     * @brief 创建控件
     */
    void CreateControls();

    /**
     * @brief 更新界面显示
     */
    void UpdateDisplay();

    /**
     * @brief 应用参数更改
     */
    void ApplyChanges();

    /**
     * @brief 重置为默认值
     */
    void ResetToDefaults();

    /**
     * @brief 应用预设
     */
    void ApplyPreset(const std::string& presetName);

    /**
     * @brief 处理命令消息
     */
    void OnCommand(WPARAM wParam);
};
