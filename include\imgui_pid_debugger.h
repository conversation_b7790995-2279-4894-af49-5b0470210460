#pragma once

#include "pid_controller.h"
#include <d3d11.h>
#include <memory>
#include <windows.h>

// ImGui头文件
#include "../imgui/imgui.h"
#include "../imgui/imgui_impl_dx11.h"
#include "../imgui/imgui_impl_win32.h"

/**
 * @brief ImGui PID参数调试器
 *
 * 提供实时调整PID参数的图形化界面
 */
class PIDDebugger {
public:
    PIDDebugger();
    ~PIDDebugger();

    /**
     * @brief 初始化ImGui调试器
     * @param device D3D11设备
     * @param deviceContext D3D11设备上下文
     * @param hwnd 窗口句柄
     * @return 是否初始化成功
     */
    bool Initialize(ID3D11Device* device, ID3D11DeviceContext* deviceContext, HWND hwnd);

    /**
     * @brief 设置PID控制器
     * @param pidController PID控制器指针
     */
    void SetPIDController(PIDController* pidController);

    /**
     * @brief 开始新的ImGui帧
     */
    void NewFrame();

    /**
     * @brief 渲染PID调试界面
     * @param isVisible 界面是否可见
     * @return 参数是否有变化
     */
    bool RenderDebugWindow(bool& isVisible);

    /**
     * @brief 结束ImGui帧并渲染
     */
    void EndFrame();

    /**
     * @brief 处理Windows消息
     */
    bool ProcessMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);

    /**
     * @brief 清理资源
     */
    void Shutdown();

private:
    ID3D11Device* m_device = nullptr;
    ID3D11DeviceContext* m_deviceContext = nullptr;
    HWND m_hwnd = nullptr;
    PIDController* m_pidController = nullptr;
    bool m_initialized = false;

    // PID参数缓存
    PIDController::PIDConfig m_configCache;
    bool m_configChanged = false;

    // 界面状态
    bool m_showBasicParams = true;
    bool m_showAdvancedParams = true;
    bool m_showAdaptiveParams = true;
    bool m_showDebugInfo = true;

    // 外部参数（从main.cpp传入）
    float* m_yOffsetRatioHead = nullptr;
    float* m_yOffsetRatioBody = nullptr;

    /**
     * @brief 渲染基本PID参数
     */
    void RenderBasicParams();

    /**
     * @brief 渲染高级参数
     */
    void RenderAdvancedParams();

    /**
     * @brief 渲染自适应参数
     */
    void RenderAdaptiveParams();

    /**
     * @brief 渲染调试信息
     */
    void RenderDebugInfo();

    /**
     * @brief 渲染预设按钮
     */
    void RenderPresets();

    /**
     * @brief 应用配置更改
     */
    void ApplyConfigChanges();

    /**
     * @brief 设置ImGui样式
     */
    void SetupImGuiStyle();

public:
    /**
     * @brief 设置外部参数引用
     */
    void SetExternalParams(float* yOffsetRatioHead, float* yOffsetRatioBody);
};
