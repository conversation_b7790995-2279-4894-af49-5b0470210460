#pragma once

#include "kmbox_controller.h"
#include "inference_engine.h"
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>

/**
 * @brief 自瞄控制器
 * 
 * 这个类将AI检测结果与KMBoxNet硬件控制结合，实现智能自瞄功能。
 * 主要功能包括：
 * - 目标选择和优先级计算
 * - PID控制器实现平滑跟踪
 * - 预测算法补偿延迟
 * - 自瞄参数配置和调优
 */
class AimController {
public:
    /**
     * @brief 自瞄配置结构体
     */
    struct AimConfig {
        // 基础设置
        bool enabled = false;                    // 是否启用自瞄
        float aimSensitivity = 1.0f;            // 瞄准灵敏度
        float aimSpeed = 0.8f;                  // 瞄准速度 (0.1-2.0)
        float aimSmooth = 0.5f;                 // 平滑度 (0.1-1.0)
        
        // 目标选择
        float maxAimDistance = 200.0f;          // 最大瞄准距离（像素）
        float headPriority = 1.5f;              // 头部目标优先级倍数
        float bodyPriority = 1.0f;              // 身体目标优先级倍数
        bool preferCloserTargets = true;        // 是否优先选择更近的目标
        
        // PID控制器参数
        float pidKp = 0.8f;                     // 比例系数
        float pidKi = 0.1f;                     // 积分系数
        float pidKd = 0.2f;                     // 微分系数
        float pidMaxIntegral = 100.0f;          // 积分限幅
        
        // 预测设置
        bool enablePrediction = true;           // 是否启用目标预测
        float predictionTime = 0.05f;           // 预测时间（秒）
        float predictionStrength = 0.7f;        // 预测强度
        
        // 触发设置
        bool autoFire = false;                  // 是否自动开火
        float autoFireDelay = 0.1f;             // 自动开火延迟（秒）
        float autoFireConfidence = 0.8f;       // 自动开火置信度阈值
        
        // 安全设置
        float maxMovePerFrame = 50.0f;          // 每帧最大移动距离（像素）
        float aimFOV = 100.0f;                  // 瞄准视野范围（像素半径）
        bool respectRecoil = true;              // 是否考虑后坐力
    };

    /**
     * @brief 目标信息结构体
     */
    struct TargetInfo {
        Detection detection;                    // 原始检测结果
        float priority = 0.0f;                  // 目标优先级
        float distance = 0.0f;                  // 距离准星的距离
        float predictedX = 0.0f;                // 预测X坐标
        float predictedY = 0.0f;                // 预测Y坐标
        bool isValid = false;                   // 目标是否有效
    };

    /**
     * @brief PID控制器状态
     */
    struct PIDState {
        float lastError = 0.0f;                 // 上次误差
        float integral = 0.0f;                  // 积分累积
        float derivative = 0.0f;                // 微分值
        std::chrono::high_resolution_clock::time_point lastTime;
    };

public:
    /**
     * @brief 构造函数
     */
    AimController();

    /**
     * @brief 析构函数
     */
    ~AimController();

    /**
     * @brief 初始化自瞄控制器
     * @param kmboxController KMBox控制器实例
     * @param config 自瞄配置
     * @return 初始化是否成功
     */
    bool Initialize(std::shared_ptr<KMBoxController> kmboxController, const AimConfig& config);

    /**
     * @brief 处理检测结果并执行自瞄
     * @param detections AI检测结果
     * @param screenCenterX 屏幕中心X坐标
     * @param screenCenterY 屏幕中心Y坐标
     * @return 处理是否成功
     */
    bool ProcessDetections(const std::vector<Detection>& detections, 
                          float screenCenterX, float screenCenterY);

    /**
     * @brief 启用/禁用自瞄
     * @param enabled 是否启用
     */
    void SetEnabled(bool enabled);

    /**
     * @brief 检查自瞄是否启用
     * @return 是否启用
     */
    bool IsEnabled() const;

    /**
     * @brief 更新配置
     * @param config 新的配置
     */
    void UpdateConfig(const AimConfig& config);

    /**
     * @brief 获取当前配置
     * @return 当前配置
     */
    AimConfig GetConfig() const;

    /**
     * @brief 获取当前目标信息
     * @return 当前目标信息，如果没有目标则返回无效目标
     */
    TargetInfo GetCurrentTarget() const;

    /**
     * @brief 重置PID控制器状态
     */
    void ResetPID();

    /**
     * @brief 设置屏幕中心坐标
     * @param centerX 中心X坐标
     * @param centerY 中心Y坐标
     */
    void SetScreenCenter(float centerX, float centerY);

private:
    // 私有成员变量
    std::shared_ptr<KMBoxController> m_kmboxController;
    AimConfig m_config;
    std::atomic<bool> m_enabled;
    
    // 目标跟踪
    TargetInfo m_currentTarget;
    std::vector<TargetInfo> m_targetHistory;
    
    // PID控制器
    PIDState m_pidX;
    PIDState m_pidY;
    
    // 屏幕信息
    float m_screenCenterX = 160.0f;  // 默认320x320的中心
    float m_screenCenterY = 160.0f;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 自动开火状态
    std::chrono::high_resolution_clock::time_point m_lastFireTime;
    bool m_autoFireActive = false;

    // 私有方法
    std::vector<TargetInfo> AnalyzeTargets(const std::vector<Detection>& detections);
    TargetInfo SelectBestTarget(const std::vector<TargetInfo>& targets);
    float CalculateTargetPriority(const Detection& detection, float distance);
    std::pair<float, float> PredictTargetPosition(const TargetInfo& target);
    std::pair<float, float> CalculatePIDOutput(float targetX, float targetY);
    bool ExecuteAim(float deltaX, float deltaY);
    bool ShouldAutoFire(const TargetInfo& target);
    void UpdateTargetHistory(const TargetInfo& target);
    float CalculateDistance(float x1, float y1, float x2, float y2);
    bool IsTargetInFOV(float targetX, float targetY);
};
