# 鼠标控制器抽象设计文档

## 概述

本文档描述了鼠标控制器的抽象设计架构，该设计允许自瞄系统支持多种不同的鼠标控制实现方式，包括硬件模拟器、系统API、品牌驱动等。

## 设计目标

1. **统一接口**：为不同的鼠标控制方式提供统一的API接口
2. **易于扩展**：可以轻松添加新的控制器实现
3. **运行时切换**：支持在运行时切换不同的控制器类型
4. **解耦合**：自瞄逻辑与具体的硬件控制实现分离
5. **可测试性**：支持模拟控制器用于单元测试

## 架构设计

### 1. 抽象基类 - MouseController

```cpp
class MouseController {
public:
    // 核心功能1：状态查询
    virtual MouseState GetMouseState() = 0;
    virtual bool IsMouseButtonPressed(MouseButton button) = 0;
    virtual bool StartMonitoring(MouseEventCallback callback) = 0;
    
    // 核心功能2：移动执行
    virtual bool MoveMouse(int deltaX, int deltaY, MoveType moveType, int duration) = 0;
    virtual bool SmoothMoveMouse(int deltaX, int deltaY, int duration) = 0;
    virtual bool BezierMoveMouse(int deltaX, int deltaY, int duration, ...) = 0;
    
    // 连接管理
    virtual bool Initialize(const std::string& configJson) = 0;
    virtual bool IsConnected() const = 0;
    virtual void Disconnect() = 0;
};
```

### 2. 具体实现类

#### KMBoxController
- **实现方式**：基于KMBoxNet硬件
- **优势**：硬件级控制，难以检测，精确度高
- **劣势**：需要额外硬件，成本较高
- **状态获取**：通过KMBox监控API
- **移动执行**：通过网络协议发送到硬件

#### SystemMouseController（待实现）
- **实现方式**：基于Windows系统API
- **优势**：无需额外硬件，兼容性好
- **劣势**：容易被检测，可能被拦截
- **状态获取**：GetAsyncKeyState、GetCursorPos
- **移动执行**：SetCursorPos、mouse_event

#### LogitechMouseController（待实现）
- **实现方式**：基于罗技鼠标驱动
- **优势**：驱动级控制，相对难检测
- **劣势**：需要特定品牌鼠标
- **状态获取**：罗技SDK或系统API
- **移动执行**：罗技驱动接口

### 3. 工厂模式

```cpp
class MouseControllerFactory {
public:
    static std::unique_ptr<MouseController> Create(MouseControllerType type);
    static std::unique_ptr<MouseController> Create(const std::string& typeName);
    static std::vector<std::string> GetSupportedTypes();
};
```

## 核心功能

### 1. 状态查询功能

每个控制器实现都必须提供以下状态查询功能：

- **GetMouseState()**: 获取完整的鼠标状态
- **IsMouseButtonPressed()**: 检查特定按键是否按下
- **StartMonitoring()**: 启动实时监控，支持回调
- **StopMonitoring()**: 停止监控

### 2. 移动执行功能

每个控制器实现都必须提供以下移动执行功能：

- **MoveMouse()**: 基础移动功能，支持多种移动类型
- **SmoothMoveMouse()**: 平滑移动
- **BezierMoveMouse()**: 贝塞尔曲线移动

## 使用示例

### 基本使用

```cpp
// 创建控制器
auto controller = MouseControllerFactory::Create("kmbox");

// 初始化配置
std::string config = R"({
    "ip": "*************",
    "port": "8808",
    "mac": "62587019"
})";

if (controller->Initialize(config)) {
    // 监听鼠标状态
    if (controller->IsMouseButtonPressed(MouseController::MouseButton::Right)) {
        // 执行移动
        controller->MoveMouse(50, 30, MouseController::MoveType::Smooth, 200);
    }
}
```

### 自瞄系统集成

```cpp
class AimBot {
    std::unique_ptr<MouseController> m_mouseController;
    
public:
    void Initialize(const std::string& controllerType) {
        m_mouseController = MouseControllerFactory::Create(controllerType);
        // 配置和连接...
    }
    
    void Update() {
        // 1. 检查用户输入
        if (m_mouseController->IsMouseButtonPressed(MouseController::MouseButton::Right)) {
            // 2. AI检测和计算
            auto targetVector = CalculateAimVector();
            
            // 3. 执行移动
            m_mouseController->MoveMouse(targetVector.x, targetVector.y, 
                                       MouseController::MoveType::Smooth, 100);
        }
    }
};
```

## 配置系统

### JSON配置格式

每种控制器类型都有自己的配置格式：

#### KMBox配置
```json
{
    "ip": "*************",
    "port": "8808",
    "mac": "62587019",
    "monitorPort": 1000,
    "maxRetries": 3,
    "retryDelayMs": 1000,
    "enableEncryption": true
}
```

#### System配置
```json
{
    "sensitivity": 1.0,
    "smoothing": true,
    "acceleration": false
}
```

## 扩展指南

### 添加新的控制器实现

1. **继承抽象基类**
```cpp
class NewMouseController : public MouseController {
    // 实现所有纯虚函数
};
```

2. **在工厂类中注册**
```cpp
// 在MouseControllerFactory::Create中添加新类型
case MouseControllerType::NewType:
    return std::make_unique<NewMouseController>();
```

3. **添加配置支持**
```cpp
// 在GetConfigForType中添加配置模板
```

## 测试

### 单元测试
- 使用MockMouseController进行单元测试
- 测试各种移动模式和状态查询功能

### 集成测试
- 使用MouseControllerTest程序进行集成测试
- 支持运行时切换不同控制器类型

## 文件结构

```
include/
├── mouse_controller.h          # 抽象基类定义
└── kmbox_controller_new.h      # KMBox实现类定义

src/
├── mouse_controller_factory.cpp    # 工厂类实现
├── kmbox_controller_new.cpp        # KMBox实现类
└── mouse_controller_example.cpp    # 使用示例和测试程序
```

## 编译和运行

### 编译
```bash
cmake --build build --config Release --target MouseControllerTest
```

### 运行
```bash
./build/Release/MouseControllerTest.exe
```

## 优势总结

1. **灵活性**：可以根据需要选择最适合的控制器类型
2. **兼容性**：支持多种硬件和软件环境
3. **可维护性**：统一接口便于维护和调试
4. **可扩展性**：易于添加新的控制器实现
5. **可测试性**：支持模拟和单元测试

## 未来计划

1. **实现SystemMouseController**：基于Windows API的实现
2. **实现LogitechMouseController**：基于罗技驱动的实现
3. **实现MockMouseController**：用于单元测试
4. **完善JSON配置解析**：支持更复杂的配置选项
5. **添加性能监控**：监控各种控制器的性能表现
