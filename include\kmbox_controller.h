#pragma once

#include <string>
#include <memory>
#include <functional>
#include <mutex>
#include <atomic>
#include <thread>

/**
 * @brief KMBoxNet硬件鼠标控制器
 * 
 * 这个类封装了KMBoxNet硬件的所有鼠标控制功能，包括：
 * - 设备连接管理
 * - 鼠标移动控制（相对移动、平滑移动、贝塞尔曲线移动）
 * - 鼠标按键控制（左键、右键、中键、侧键）
 * - 鼠标状态监控（按键状态、位置、滚轮）
 * - 错误处理和自动重连
 */
class KMBoxController {
public:
    /**
     * @brief 鼠标按键枚举
     */
    enum class MouseButton {
        Left = 1,
        Right = 2,
        Middle = 4,
        Side1 = 8,
        Side2 = 16
    };

    /**
     * @brief 连接状态枚举
     */
    enum class ConnectionStatus {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    /**
     * @brief 鼠标状态结构体
     */
    struct MouseState {
        bool leftButton = false;
        bool rightButton = false;
        bool middleButton = false;
        bool side1Button = false;
        bool side2Button = false;
        int x = 0;
        int y = 0;
        int wheel = 0;
    };

    /**
     * @brief 设备配置结构体
     */
    struct DeviceConfig {
        std::string ip = "*************";
        std::string port = "8808";
        std::string mac = "62587019";
        int monitorPort = 1000;
        int maxRetries = 3;
        int retryDelayMs = 1000;
        bool enableEncryption = true;  // 是否使用加密API
    };

    /**
     * @brief 错误回调函数类型
     */
    using ErrorCallback = std::function<void(int errorCode, const std::string& errorMessage)>;

    /**
     * @brief 鼠标事件回调函数类型
     */
    using MouseEventCallback = std::function<void(const MouseState& state)>;

public:
    /**
     * @brief 构造函数
     */
    KMBoxController();

    /**
     * @brief 析构函数
     */
    ~KMBoxController();

    /**
     * @brief 连接KMBoxNet设备
     * @param config 设备配置
     * @return 连接是否成功
     */
    bool Connect(const DeviceConfig& config);

    /**
     * @brief 断开连接
     */
    void Disconnect();

    /**
     * @brief 检查连接状态
     * @return 当前连接状态
     */
    ConnectionStatus GetConnectionStatus() const;

    /**
     * @brief 检查是否已连接
     * @return 是否已连接
     */
    bool IsConnected() const;

    // ==================== 鼠标移动控制 ====================

    /**
     * @brief 相对移动鼠标
     * @param deltaX X轴移动距离（像素）
     * @param deltaY Y轴移动距离（像素）
     * @return 操作是否成功
     */
    bool MoveMouse(int deltaX, int deltaY);

    /**
     * @brief 平滑移动鼠标到目标位置
     * @param deltaX X轴移动距离（像素）
     * @param deltaY Y轴移动距离（像素）
     * @param durationMs 移动持续时间（毫秒）
     * @return 操作是否成功
     */
    bool SmoothMoveMouse(int deltaX, int deltaY, int durationMs = 500);

    /**
     * @brief 使用贝塞尔曲线移动鼠标
     * @param deltaX 终点X坐标
     * @param deltaY 终点Y坐标
     * @param durationMs 移动时间
     * @param controlX1 控制点1 X坐标
     * @param controlY1 控制点1 Y坐标
     * @param controlX2 控制点2 X坐标
     * @param controlY2 控制点2 Y坐标
     * @return 操作是否成功
     */
    bool BezierMoveMouse(int deltaX, int deltaY, int durationMs, 
                        int controlX1, int controlY1, int controlX2, int controlY2);

    // ==================== 鼠标按键控制 ====================

    /**
     * @brief 按下鼠标按键
     * @param button 按键类型
     * @return 操作是否成功
     */
    bool PressMouseButton(MouseButton button);

    /**
     * @brief 松开鼠标按键
     * @param button 按键类型
     * @return 操作是否成功
     */
    bool ReleaseMouseButton(MouseButton button);

    /**
     * @brief 点击鼠标按键
     * @param button 按键类型
     * @param holdTimeMs 按住时间（毫秒）
     * @return 操作是否成功
     */
    bool ClickMouseButton(MouseButton button, int holdTimeMs = 100);

    /**
     * @brief 滚动鼠标滚轮
     * @param direction 滚动方向（正数向上，负数向下）
     * @param steps 滚动步数
     * @return 操作是否成功
     */
    bool ScrollWheel(int direction, int steps = 3);

    // ==================== 鼠标状态监控 ====================

    /**
     * @brief 启动鼠标监控
     * @param callback 鼠标事件回调函数
     * @return 启动是否成功
     */
    bool StartMonitoring(MouseEventCallback callback = nullptr);

    /**
     * @brief 停止鼠标监控
     */
    void StopMonitoring();

    /**
     * @brief 获取当前鼠标状态
     * @return 鼠标状态
     */
    MouseState GetMouseState();

    /**
     * @brief 检查指定按键是否按下
     * @param button 按键类型
     * @return 按键是否按下
     */
    bool IsMouseButtonPressed(MouseButton button);

    // ==================== 高级功能 ====================

    /**
     * @brief 拖拽操作
     * @param startX 起始X坐标
     * @param startY 起始Y坐标
     * @param endX 结束X坐标
     * @param endY 结束Y坐标
     * @param durationMs 拖拽持续时间
     * @return 操作是否成功
     */
    bool DragMouse(int startX, int startY, int endX, int endY, int durationMs = 500);

    /**
     * @brief 画圆轨迹
     * @param radius 半径
     * @param steps 步数
     * @param clockwise 是否顺时针
     * @return 操作是否成功
     */
    bool DrawCircle(int radius, int steps = 360, bool clockwise = true);

    // ==================== 配置和回调 ====================

    /**
     * @brief 设置错误回调函数
     * @param callback 错误回调函数
     */
    void SetErrorCallback(ErrorCallback callback);

    /**
     * @brief 获取最后一次错误码
     * @return 错误码
     */
    int GetLastError() const;

    /**
     * @brief 获取最后一次错误消息
     * @return 错误消息
     */
    std::string GetLastErrorMessage() const;

private:
    // 私有成员变量
    DeviceConfig m_config;
    std::atomic<ConnectionStatus> m_connectionStatus;
    std::atomic<bool> m_monitoring;
    std::atomic<int> m_lastError;
    std::string m_lastErrorMessage;
    
    // 线程安全
    mutable std::mutex m_mutex;
    std::unique_ptr<std::thread> m_monitorThread;
    
    // 回调函数
    ErrorCallback m_errorCallback;
    MouseEventCallback m_mouseEventCallback;

    // 私有方法
    bool ConnectWithRetry();
    void HandleError(int errorCode, const std::string& message);
    void MonitorThreadFunction();
    bool ExecuteWithRetry(std::function<int()> operation, int maxRetries = 2);
    std::string GetErrorMessage(int errorCode) const;
};
