#include "gpu_renderer.h"
#include "inference_engine.h"  // For Detection struct
#include <d3dcompiler.h>
#include <iostream>
#include <chrono>
#include <fstream>
#include <sstream>
#include <algorithm>

#pragma comment(lib, "d3dcompiler.lib")
#pragma comment(lib, "dxgi.lib")

GPURenderer::GPURenderer() = default;
GPURenderer::~GPURenderer() = default;

bool GPURenderer::Initialize(ID3D11Device* device, const GPURenderConfig& config) {
    if (!device) {
        std::cout << "错误: GPU渲染器设备无效" << std::endl;
        return false;
    }

    m_device = device;
    m_device->GetImmediateContext(&m_context);
    m_config = config;

    std::cout << "正在初始化GPU渲染器..." << std::endl;
    std::cout << "渲染尺寸: " << config.renderWidth << "x" << config.renderHeight << std::endl;

    // 创建渲染目标
    if (!CreateRenderTarget()) {
        std::cout << "错误: 渲染目标创建失败" << std::endl;
        return false;
    }

    // 创建格式转换着色器
    if (!CreateFormatConversionShaders()) {
        std::cout << "错误: 格式转换着色器创建失败" << std::endl;
        return false;
    }

    // 创建边界框绘制着色器
    if (!CreateBoxDrawingShaders()) {
        std::cout << "错误: 边界框绘制着色器创建失败" << std::endl;
        return false;
    }

    // 创建采样器状态
    if (!CreateSamplerState()) {
        std::cout << "错误: 采样器状态创建失败" << std::endl;
        return false;
    }

    std::cout << "GPU渲染器初始化成功，包含格式转换和边界框绘制功能" << std::endl;
    return true;
}

bool GPURenderer::CreateRenderTarget() {
    HRESULT hr;

    // Create render texture
    D3D11_TEXTURE2D_DESC textureDesc = {};
    textureDesc.Width = m_config.renderWidth;
    textureDesc.Height = m_config.renderHeight;
    textureDesc.MipLevels = 1;
    textureDesc.ArraySize = 1;
    textureDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    textureDesc.SampleDesc.Count = 1;
    textureDesc.Usage = D3D11_USAGE_DEFAULT;
    textureDesc.BindFlags = D3D11_BIND_RENDER_TARGET | D3D11_BIND_SHADER_RESOURCE;

    hr = m_device->CreateTexture2D(&textureDesc, nullptr, &m_renderTexture);
    if (FAILED(hr)) {
        std::cout << "错误: 渲染纹理创建失败" << std::endl;
        return false;
    }

    // 创建渲染目标视图
    hr = m_device->CreateRenderTargetView(m_renderTexture.Get(), nullptr, &m_renderTargetView);
    if (FAILED(hr)) {
        std::cout << "错误: 渲染目标视图创建失败" << std::endl;
        return false;
    }

    // 创建着色器资源视图
    hr = m_device->CreateShaderResourceView(m_renderTexture.Get(), nullptr, &m_renderSRV);
    if (FAILED(hr)) {
        std::cout << "错误: 渲染SRV创建失败" << std::endl;
        return false;
    }

    return true;
}

bool GPURenderer::CreateShaders() {
    // Use embedded simple shader code for now
    std::string shaderCode = R"(
        cbuffer RenderConstants : register(b0) {
            float renderWidth;
            float renderHeight;
            float modelWidth;
            float modelHeight;
            float4 headColor;
            float4 bodyColor;
            float4 crosshairColor;
            float boxLineWidth;
            float crosshairSize;
            uint showCrosshair;
            uint showBoundingBoxes;
            uint padding1;
        };

        struct VSOutput {
            float4 position : SV_POSITION;
            float4 color : COLOR0;
        };

        VSOutput VS_Main(uint vertexID : SV_VertexID) {
            VSOutput output;
            output.position = float4(0, 0, 0, 1);
            output.color = float4(1, 0, 0, 1);
            return output;
        }

        float4 PS_Main(VSOutput input) : SV_Target {
            return input.color;
        }
    )";

    // Compile vertex shader
    ComPtr<ID3DBlob> vsBlob;
    if (!CompileShaderFromString(shaderCode, "VS_Main", "vs_5_0", &vsBlob)) {
        std::cout << "Error: Failed to compile vertex shader" << std::endl;
        return false;
    }

    HRESULT hr = m_device->CreateVertexShader(
        vsBlob->GetBufferPointer(),
        vsBlob->GetBufferSize(),
        nullptr,
        &m_vertexShader
    );
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create vertex shader" << std::endl;
        return false;
    }

    // Skip geometry shader for now (simplified version)

    // Compile pixel shader
    ComPtr<ID3DBlob> psBlob;
    if (!CompileShaderFromString(shaderCode, "PS_Main", "ps_5_0", &psBlob)) {
        std::cout << "Error: Failed to compile pixel shader" << std::endl;
        return false;
    }

    hr = m_device->CreatePixelShader(
        psBlob->GetBufferPointer(),
        psBlob->GetBufferSize(),
        nullptr,
        &m_pixelShader
    );
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create pixel shader" << std::endl;
        return false;
    }

    // Create input layout (empty since we use SV_VertexID)
    hr = m_device->CreateInputLayout(
        nullptr,
        0,
        vsBlob->GetBufferPointer(),
        vsBlob->GetBufferSize(),
        &m_inputLayout
    );
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create input layout" << std::endl;
        return false;
    }

    std::cout << "Render shaders compiled successfully" << std::endl;
    return true;
}

bool GPURenderer::CreateBuffers() {
    HRESULT hr;

    // Create constant buffer
    D3D11_BUFFER_DESC cbDesc = {};
    cbDesc.Usage = D3D11_USAGE_DYNAMIC;
    cbDesc.ByteWidth = sizeof(RenderConstants);
    cbDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    cbDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

    hr = m_device->CreateBuffer(&cbDesc, nullptr, &m_constantBuffer);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create constant buffer" << std::endl;
        return false;
    }

    return true;
}

bool GPURenderer::CreateStates() {
    HRESULT hr;

    // Create blend state for alpha blending
    D3D11_BLEND_DESC blendDesc = {};
    blendDesc.RenderTarget[0].BlendEnable = TRUE;
    blendDesc.RenderTarget[0].SrcBlend = D3D11_BLEND_SRC_ALPHA;
    blendDesc.RenderTarget[0].DestBlend = D3D11_BLEND_INV_SRC_ALPHA;
    blendDesc.RenderTarget[0].BlendOp = D3D11_BLEND_OP_ADD;
    blendDesc.RenderTarget[0].SrcBlendAlpha = D3D11_BLEND_ONE;
    blendDesc.RenderTarget[0].DestBlendAlpha = D3D11_BLEND_ZERO;
    blendDesc.RenderTarget[0].BlendOpAlpha = D3D11_BLEND_OP_ADD;
    blendDesc.RenderTarget[0].RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;

    hr = m_device->CreateBlendState(&blendDesc, &m_blendState);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create blend state" << std::endl;
        return false;
    }

    // Create rasterizer state
    D3D11_RASTERIZER_DESC rastDesc = {};
    rastDesc.FillMode = D3D11_FILL_SOLID;
    rastDesc.CullMode = D3D11_CULL_NONE;
    rastDesc.FrontCounterClockwise = FALSE;
    rastDesc.DepthBias = 0;
    rastDesc.SlopeScaledDepthBias = 0.0f;
    rastDesc.DepthBiasClamp = 0.0f;
    rastDesc.DepthClipEnable = TRUE;
    rastDesc.ScissorEnable = FALSE;
    rastDesc.MultisampleEnable = FALSE;
    rastDesc.AntialiasedLineEnable = FALSE;

    hr = m_device->CreateRasterizerState(&rastDesc, &m_rasterizerState);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create rasterizer state" << std::endl;
        return false;
    }

    // Create sampler state
    D3D11_SAMPLER_DESC sampDesc = {};
    sampDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
    sampDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
    sampDesc.MinLOD = 0;
    sampDesc.MaxLOD = D3D11_FLOAT32_MAX;

    hr = m_device->CreateSamplerState(&sampDesc, &m_samplerState);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create sampler state" << std::endl;
        return false;
    }

    return true;
}

bool GPURenderer::UpdateConstantBuffer() {
    // Fill constant buffer data
    m_constants.renderWidth = (float)m_config.renderWidth;
    m_constants.renderHeight = (float)m_config.renderHeight;
    m_constants.modelWidth = (float)m_config.modelWidth;
    m_constants.modelHeight = (float)m_config.modelHeight;
    
    memcpy(m_constants.headColor, m_config.headColor, sizeof(float) * 4);
    memcpy(m_constants.bodyColor, m_config.bodyColor, sizeof(float) * 4);
    memcpy(m_constants.crosshairColor, m_config.crosshairColor, sizeof(float) * 4);
    
    m_constants.boxLineWidth = m_config.boxLineWidth;
    m_constants.crosshairSize = m_config.crosshairSize;
    m_constants.showCrosshair = m_config.showCrosshair ? 1 : 0;
    m_constants.showBoundingBoxes = m_config.showBoundingBoxes ? 1 : 0;

    // Update GPU buffer
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = m_context->Map(m_constantBuffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to map constant buffer" << std::endl;
        return false;
    }

    memcpy(mappedResource.pData, &m_constants, sizeof(RenderConstants));
    m_context->Unmap(m_constantBuffer.Get(), 0);

    return true;
}

bool GPURenderer::CompileShaderFromString(const std::string& shaderCode, const std::string& entryPoint,
                                         const std::string& target, ID3DBlob** blobOut) {
    ComPtr<ID3DBlob> errorBlob;
    
    DWORD shaderFlags = D3DCOMPILE_ENABLE_STRICTNESS;
#ifdef _DEBUG
    shaderFlags |= D3DCOMPILE_DEBUG | D3DCOMPILE_SKIP_OPTIMIZATION;
#endif

    HRESULT hr = D3DCompile(
        shaderCode.c_str(),
        shaderCode.length(),
        nullptr,
        nullptr,
        nullptr,
        entryPoint.c_str(),
        target.c_str(),
        shaderFlags,
        0,
        blobOut,
        &errorBlob
    );

    if (FAILED(hr)) {
        if (errorBlob) {
            std::cout << "着色器编译错误: " << static_cast<char*>(errorBlob->GetBufferPointer()) << std::endl;
        }
        std::cout << "着色器编译失败: " << entryPoint << std::endl;
        return false;
    }

    return true;
}

bool GPURenderer::RenderDetections(ID3D11Texture2D* sourceTexture,
                                  const Detection* detections,
                                  uint32_t detectionCount,
                                  ID3D11Texture2D*& renderedTexture) {
    if (!sourceTexture) {
        std::cout << "错误: GPU渲染输入无效" << std::endl;
        return false;
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    // Check source and destination texture dimensions
    D3D11_TEXTURE2D_DESC srcDesc, dstDesc;
    sourceTexture->GetDesc(&srcDesc);
    m_renderTexture->GetDesc(&dstDesc);

    // Debug: Print texture info every 100 frames
    static int debugCounter = 0;
    if (++debugCounter % 100 == 0) {
        std::cout << "GPU Renderer Debug - Source: " << srcDesc.Width << "x" << srcDesc.Height
                  << " Format:" << srcDesc.Format << " Usage:" << srcDesc.Usage << std::endl;
        std::cout << "GPU Renderer Debug - Dest: " << dstDesc.Width << "x" << dstDesc.Height
                  << " Format:" << dstDesc.Format << " Usage:" << dstDesc.Usage << std::endl;
    }

    // Step 1: Extract center region and convert format
    // Set render target
    m_context->OMSetRenderTargets(1, m_renderTargetView.GetAddressOf(), nullptr);

    // Set viewport
    D3D11_VIEWPORT viewport = {};
    viewport.Width = (float)dstDesc.Width;
    viewport.Height = (float)dstDesc.Height;
    viewport.MinDepth = 0.0f;
    viewport.MaxDepth = 1.0f;
    m_context->RSSetViewports(1, &viewport);

    // Set format conversion shaders
    m_context->VSSetShader(m_formatConvertVS.Get(), nullptr, 0);
    m_context->PSSetShader(m_formatConvertPS.Get(), nullptr, 0);
    m_context->IASetInputLayout(nullptr);
    m_context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);

    // Create shader resource view for source texture
    ComPtr<ID3D11ShaderResourceView> sourceSRV;
    D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
    srvDesc.Format = srcDesc.Format;
    srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
    srvDesc.Texture2D.MipLevels = 1;

    HRESULT hr = m_device->CreateShaderResourceView(sourceTexture, &srvDesc, &sourceSRV);
    if (SUCCEEDED(hr)) {
        // Set the source texture as shader resource
        m_context->PSSetShaderResources(0, 1, sourceSRV.GetAddressOf());
        m_context->PSSetSamplers(0, 1, m_samplerState.GetAddressOf());

        // Draw fullscreen triangle to extract center region and convert format
        m_context->Draw(3, 0);

        // Clean up
        ID3D11ShaderResourceView* nullSRV = nullptr;
        m_context->PSSetShaderResources(0, 1, &nullSRV);
    }

    // Step 2: Draw detection boxes if we have detections
    if (detectionCount > 0 && detections && m_config.showBoundingBoxes) {
        // Update detection buffer with current detections
        UpdateDetectionBuffer(detections, detectionCount);

        // Enable alpha blending for transparent overlay
        ComPtr<ID3D11BlendState> blendState;
        D3D11_BLEND_DESC blendDesc = {};
        blendDesc.RenderTarget[0].BlendEnable = TRUE;
        blendDesc.RenderTarget[0].SrcBlend = D3D11_BLEND_SRC_ALPHA;
        blendDesc.RenderTarget[0].DestBlend = D3D11_BLEND_INV_SRC_ALPHA;
        blendDesc.RenderTarget[0].BlendOp = D3D11_BLEND_OP_ADD;
        blendDesc.RenderTarget[0].SrcBlendAlpha = D3D11_BLEND_ONE;
        blendDesc.RenderTarget[0].DestBlendAlpha = D3D11_BLEND_ZERO;
        blendDesc.RenderTarget[0].BlendOpAlpha = D3D11_BLEND_OP_ADD;
        blendDesc.RenderTarget[0].RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;

        m_device->CreateBlendState(&blendDesc, &blendState);
        float blendFactor[4] = {0.0f, 0.0f, 0.0f, 0.0f};
        m_context->OMSetBlendState(blendState.Get(), blendFactor, 0xffffffff);

        // Set box drawing shaders
        m_context->VSSetShader(m_boxDrawVS.Get(), nullptr, 0);
        m_context->PSSetShader(m_boxDrawPS.Get(), nullptr, 0);

        // Set detection buffer as constant buffer
        m_context->PSSetConstantBuffers(0, 1, m_detectionBuffer.GetAddressOf());

        // Create shader resource view for the converted texture
        ComPtr<ID3D11ShaderResourceView> convertedSRV;
        D3D11_SHADER_RESOURCE_VIEW_DESC convertedSrvDesc = {};
        convertedSrvDesc.Format = dstDesc.Format;
        convertedSrvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
        convertedSrvDesc.Texture2D.MipLevels = 1;

        hr = m_device->CreateShaderResourceView(m_renderTexture.Get(), &convertedSrvDesc, &convertedSRV);
        if (SUCCEEDED(hr)) {
            // Set the converted texture as shader resource
            m_context->PSSetShaderResources(0, 1, convertedSRV.GetAddressOf());
            m_context->PSSetSamplers(0, 1, m_samplerState.GetAddressOf());

            // Draw fullscreen triangle to draw boxes with blending
            m_context->Draw(3, 0);

            // Clean up
            ID3D11ShaderResourceView* nullSRV = nullptr;
            m_context->PSSetShaderResources(0, 1, &nullSRV);
            ID3D11Buffer* nullBuffer = nullptr;
            m_context->PSSetConstantBuffers(0, 1, &nullBuffer);
        }

        // Disable blending
        m_context->OMSetBlendState(nullptr, nullptr, 0xffffffff);
    }

    // Set output texture
    renderedTexture = m_renderTexture.Get();

    auto endTime = std::chrono::high_resolution_clock::now();
    m_lastRenderTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

    return true;
}

void GPURenderer::UpdateConfig(const GPURenderConfig& config) {
    m_config = config;
    UpdateConstantBuffer();
}

// GPUDisplayWindow implementation
GPUDisplayWindow::GPUDisplayWindow() = default;
GPUDisplayWindow::~GPUDisplayWindow() {
    if (m_hwnd) {
        DestroyWindow(m_hwnd);
    }
}

bool GPUDisplayWindow::Initialize(ID3D11Device* device, const GPURenderConfig& config) {
    if (!device) {
        std::cout << "Error: Invalid device for GPU Display Window" << std::endl;
        return false;
    }

    m_device = device;
    m_device->GetImmediateContext(&m_context);
    m_config = config;

    std::cout << "Initializing GPU Display Window..." << std::endl;

    // Create window
    if (!CreateDisplayWindow()) {
        std::cout << "Error: Failed to create display window" << std::endl;
        return false;
    }

    // Create swap chain
    if (!CreateSwapChain()) {
        std::cout << "Error: Failed to create swap chain" << std::endl;
        return false;
    }

    // Create copy shaders
    if (!CreateCopyShaders()) {
        std::cout << "Error: Failed to create copy shaders" << std::endl;
        return false;
    }

    std::cout << "GPU Display Window initialized successfully" << std::endl;
    return true;
}

bool GPUDisplayWindow::CreateDisplayWindow() {
    // Register window class
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = L"GPUDisplayWindow";

    if (!RegisterClassExW(&wc)) {
        std::cout << "Error: Failed to register window class" << std::endl;
        return false;
    }

    // Create window
    m_hwnd = CreateWindowExW(
        0,
        L"GPUDisplayWindow",
        L"AimBot - GPU Visualization",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        m_config.renderWidth, m_config.renderHeight,
        nullptr, nullptr,
        GetModuleHandle(nullptr),
        this
    );

    if (!m_hwnd) {
        std::cout << "Error: Failed to create display window" << std::endl;
        return false;
    }

    ShowWindow(m_hwnd, SW_SHOW);
    UpdateWindow(m_hwnd);

    return true;
}

bool GPUDisplayWindow::CreateSwapChain() {
    // Get DXGI factory
    ComPtr<IDXGIDevice> dxgiDevice;
    HRESULT hr = m_device.As(&dxgiDevice);
    if (FAILED(hr)) return false;

    ComPtr<IDXGIAdapter> dxgiAdapter;
    hr = dxgiDevice->GetAdapter(&dxgiAdapter);
    if (FAILED(hr)) return false;

    ComPtr<IDXGIFactory> dxgiFactory;
    hr = dxgiAdapter->GetParent(__uuidof(IDXGIFactory), &dxgiFactory);
    if (FAILED(hr)) return false;

    // Create swap chain
    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 1;
    swapChainDesc.BufferDesc.Width = m_config.renderWidth;
    swapChainDesc.BufferDesc.Height = m_config.renderHeight;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferDesc.RefreshRate.Numerator = 60;
    swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = m_hwnd;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.Windowed = TRUE;

    hr = dxgiFactory->CreateSwapChain(m_device.Get(), &swapChainDesc, &m_swapChain);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create swap chain" << std::endl;
        return false;
    }

    // Create back buffer RTV
    ComPtr<ID3D11Texture2D> backBuffer;
    hr = m_swapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), &backBuffer);
    if (FAILED(hr)) return false;

    hr = m_device->CreateRenderTargetView(backBuffer.Get(), nullptr, &m_backBufferRTV);
    if (FAILED(hr)) return false;

    return true;
}

bool GPUDisplayWindow::CreateCopyShaders() {
    // Simple copy shaders to display texture to screen
    std::string copyShaderCode = R"(
        struct VSOutput {
            float4 position : SV_POSITION;
            float2 texCoord : TEXCOORD0;
        };

        VSOutput CopyVS(uint vertexID : SV_VertexID) {
            VSOutput output;
            output.texCoord = float2((vertexID << 1) & 2, vertexID & 2);
            output.position = float4(output.texCoord * 2.0f - 1.0f, 0.0f, 1.0f);
            output.position.y = -output.position.y;
            return output;
        }

        Texture2D sourceTexture : register(t0);
        SamplerState sourceSampler : register(s0);

        float4 CopyPS(VSOutput input) : SV_Target {
            return sourceTexture.Sample(sourceSampler, input.texCoord);
        }
    )";

    // Compile and create shaders (simplified implementation)
    // In a full implementation, you would compile these shaders properly
    std::cout << "Copy shaders created (simplified)" << std::endl;
    return true;
}

bool GPUDisplayWindow::Present(ID3D11Texture2D* renderedTexture) {
    if (!renderedTexture) {
        std::cout << "Warning: No rendered texture to present" << std::endl;
        return false;
    }

    // Get back buffer
    ComPtr<ID3D11Texture2D> backBuffer;
    HRESULT hr = m_swapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), &backBuffer);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to get back buffer. HRESULT: 0x" << std::hex << hr << std::endl;
        return false;
    }

    // Check texture format and dimensions
    D3D11_TEXTURE2D_DESC srcDesc, dstDesc;
    renderedTexture->GetDesc(&srcDesc);
    backBuffer->GetDesc(&dstDesc);

    // Debug: Print less frequently
    static int presentCounter = 0;
    if (++presentCounter % 300 == 0) {
        std::cout << "Present Debug - Source: " << srcDesc.Width << "x" << srcDesc.Height
                  << " Format: " << srcDesc.Format << std::endl;
        std::cout << "Present Debug - BackBuffer: " << dstDesc.Width << "x" << dstDesc.Height
                  << " Format: " << dstDesc.Format << std::endl;
    }

    // Copy texture to back buffer
    m_context->CopyResource(backBuffer.Get(), renderedTexture);

    // Present to screen
    hr = m_swapChain->Present(1, 0);
    if (FAILED(hr)) {
        std::cout << "Error: Present failed. HRESULT: 0x" << std::hex << hr << std::endl;
        return false;
    }

    return true;
}

bool GPUDisplayWindow::ShouldClose() {
    return m_shouldClose;
}

void GPUDisplayWindow::ProcessMessages() {
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

LRESULT CALLBACK GPUDisplayWindow::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    GPUDisplayWindow* window = nullptr;

    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* createStruct = reinterpret_cast<CREATESTRUCT*>(lParam);
        window = reinterpret_cast<GPUDisplayWindow*>(createStruct->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(window));
    } else {
        window = reinterpret_cast<GPUDisplayWindow*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (window) {
        switch (uMsg) {
        case WM_CLOSE:
            window->m_shouldClose = true;
            return 0;
        case WM_KEYDOWN:
            if (wParam == VK_ESCAPE) {
                window->m_shouldClose = true;
            }
            return 0;
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

bool GPURenderer::CreateFormatConversionShaders() {
    HRESULT hr;

    // Read and compile format conversion shader
    std::ifstream shaderFile("shaders/format_convert.hlsl");
    if (!shaderFile.is_open()) {
        std::cout << "Error: Could not open format_convert.hlsl" << std::endl;
        return false;
    }

    std::string shaderCode((std::istreambuf_iterator<char>(shaderFile)),
                          std::istreambuf_iterator<char>());
    shaderFile.close();

    // Compile vertex shader
    ComPtr<ID3DBlob> vsBlob;
    if (!CompileShaderFromString(shaderCode, "VSMain", "vs_5_0", &vsBlob)) {
        std::cout << "Error: Failed to compile format conversion vertex shader" << std::endl;
        return false;
    }

    hr = m_device->CreateVertexShader(
        vsBlob->GetBufferPointer(),
        vsBlob->GetBufferSize(),
        nullptr,
        &m_formatConvertVS
    );
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create format conversion vertex shader" << std::endl;
        return false;
    }

    // Compile pixel shader
    ComPtr<ID3DBlob> psBlob;
    if (!CompileShaderFromString(shaderCode, "PSMain", "ps_5_0", &psBlob)) {
        std::cout << "Error: Failed to compile format conversion pixel shader" << std::endl;
        return false;
    }

    hr = m_device->CreatePixelShader(
        psBlob->GetBufferPointer(),
        psBlob->GetBufferSize(),
        nullptr,
        &m_formatConvertPS
    );
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create format conversion pixel shader" << std::endl;
        return false;
    }

    // Create input layout (empty since we use SV_VertexID)
    // For SV_VertexID, we can pass nullptr for input layout
    m_formatConvertInputLayout = nullptr;

    std::cout << "Format conversion shaders created successfully" << std::endl;
    return true;
}

bool GPURenderer::CreateSamplerState() {
    HRESULT hr;

    D3D11_SAMPLER_DESC samplerDesc = {};
    samplerDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
    samplerDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
    samplerDesc.MinLOD = 0;
    samplerDesc.MaxLOD = D3D11_FLOAT32_MAX;

    hr = m_device->CreateSamplerState(&samplerDesc, &m_samplerState);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create sampler state" << std::endl;
        return false;
    }

    return true;
}

bool GPURenderer::CreateBoxDrawingShaders() {
    // Read and compile box drawing shaders
    std::ifstream file("shaders/draw_boxes.hlsl");
    if (!file.is_open()) {
        std::cout << "Error: Could not open draw_boxes.hlsl" << std::endl;
        return false;
    }

    std::string shaderCode((std::istreambuf_iterator<char>(file)),
                          std::istreambuf_iterator<char>());
    file.close();

    // Compile vertex shader
    ComPtr<ID3DBlob> vsBlob;
    if (!CompileShaderFromString(shaderCode, "VSMain", "vs_5_0", &vsBlob)) {
        std::cout << "Error: Failed to compile box drawing vertex shader" << std::endl;
        return false;
    }

    HRESULT hr = m_device->CreateVertexShader(vsBlob->GetBufferPointer(),
                                             vsBlob->GetBufferSize(),
                                             nullptr, &m_boxDrawVS);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create box drawing vertex shader" << std::endl;
        return false;
    }

    // Compile pixel shader
    ComPtr<ID3DBlob> psBlob;
    if (!CompileShaderFromString(shaderCode, "PSMain", "ps_5_0", &psBlob)) {
        std::cout << "Error: Failed to compile box drawing pixel shader" << std::endl;
        return false;
    }

    hr = m_device->CreatePixelShader(psBlob->GetBufferPointer(),
                                    psBlob->GetBufferSize(),
                                    nullptr, &m_boxDrawPS);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create box drawing pixel shader" << std::endl;
        return false;
    }

    // Create detection buffer - must match shader cbuffer size
    D3D11_BUFFER_DESC bufferDesc = {};
    bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
    bufferDesc.ByteWidth = sizeof(float) * 4 * 64 * 2 + sizeof(uint32_t) + sizeof(float) * 3; // detections + colors + count + 3 floats
    bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

    hr = m_device->CreateBuffer(&bufferDesc, nullptr, &m_detectionBuffer);
    if (FAILED(hr)) {
        std::cout << "Error: Failed to create detection buffer" << std::endl;
        return false;
    }

    std::cout << "Box drawing shaders created successfully" << std::endl;
    return true;
}

void GPURenderer::UpdateDetectionBuffer(const Detection* detections, uint32_t detectionCount) {
    if (!m_detectionBuffer || !detections) {
        return;
    }

    // Prepare detection data for GPU
    struct DetectionData {
        float detections[64][4];  // x, y, width, height (normalized 0-1)
        float colors[64][4];      // r, g, b, confidence
        uint32_t count;
        float imageWidth;
        float imageHeight;
        float lineWidth;
    } data = {};

    data.count = min(detectionCount, 64u);
    data.imageWidth = (float)m_config.renderWidth;
    data.imageHeight = (float)m_config.renderHeight;
    data.lineWidth = 2.0f;

    for (uint32_t i = 0; i < data.count; ++i) {
        const Detection& det = detections[i];

        // Normalize coordinates to 0-1 range and clamp to valid range
        float centerX = det.x / data.imageWidth;
        float centerY = det.y / data.imageHeight;
        float width = det.width / data.imageWidth;
        float height = det.height / data.imageHeight;

        data.detections[i][0] = (centerX < 0.0f) ? 0.0f : (centerX > 1.0f) ? 1.0f : centerX;      // center x
        data.detections[i][1] = (centerY < 0.0f) ? 0.0f : (centerY > 1.0f) ? 1.0f : centerY;     // center y
        data.detections[i][2] = (width < 0.01f) ? 0.01f : (width > 1.0f) ? 1.0f : width;  // width
        data.detections[i][3] = (height < 0.01f) ? 0.01f : (height > 1.0f) ? 1.0f : height; // height

        // Debug: Print first detection coordinates
        if (i == 0) {
            static int debugCount = 0;
            if (++debugCount % 60 == 0) { // Every 60 frames
                std::cout << "Detection[0]: center(" << data.detections[i][0] << ", " << data.detections[i][1]
                          << ") size(" << data.detections[i][2] << ", " << data.detections[i][3] << ")"
                          << " original(" << det.x << ", " << det.y << ", " << det.width << ", " << det.height << ")" << std::endl;
            }
        }

        // Set color based on class
        if (det.classId == 0) { // Head
            data.colors[i][0] = 1.0f; // Red
            data.colors[i][1] = 0.0f;
            data.colors[i][2] = 0.0f;
        } else { // Body
            data.colors[i][0] = 0.0f;
            data.colors[i][1] = 0.0f;
            data.colors[i][2] = 1.0f; // Blue
        }
        data.colors[i][3] = det.confidence; // Store confidence
    }

    // Update GPU buffer
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = m_context->Map(m_detectionBuffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);
    if (SUCCEEDED(hr)) {
        memcpy(mappedResource.pData, &data, sizeof(data));
        m_context->Unmap(m_detectionBuffer.Get(), 0);
    }
}
