#include "kmbox_controller_new.h"
#include "kmbox/kmboxNet.h"
#include <iostream>
#include <chrono>
#include <cmath>
#include <Windows.h>
#include <sstream>

// 错误码定义（来自kmboxNet.h的枚举值）
const int ERR_CREAT_SOCKET = -9000;
const int ERR_NET_VERSION = -8999;
const int ERR_NET_TX = -8998;
const int ERR_NET_RX_TIMEOUT = -8997;
const int ERR_NET_CMD = -8996;
const int ERR_NET_PTS = -8995;

KMBoxController::KMBoxController()
    : m_connectionStatus(ConnectionStatus::Disconnected)
    , m_monitoring(false)
    , m_lastError(0)
    , m_lastErrorMessage("")
{
}

KMBoxController::~KMBoxController() {
    Disconnect();
}

// ==================== 连接管理（实现抽象接口） ====================

bool KMBoxController::Initialize(const std::string& configJson) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        m_config = ParseConfigJson(configJson);
    } catch (const std::exception& e) {
        HandleError(-1, "配置解析失败: " + std::string(e.what()));
        return false;
    }
    
    m_connectionStatus = ConnectionStatus::Connecting;
    
    std::cout << "正在连接KMBoxNet设备..." << std::endl;
    std::cout << "IP: " << m_config.ip << ", Port: " << m_config.port << ", MAC: " << m_config.mac << std::endl;
    
    return ConnectWithRetry();
}

bool KMBoxController::Connect(const KMBoxConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_config = config;
    m_connectionStatus = ConnectionStatus::Connecting;
    
    std::cout << "正在连接KMBoxNet设备..." << std::endl;
    std::cout << "IP: " << config.ip << ", Port: " << config.port << ", MAC: " << config.mac << std::endl;
    
    return ConnectWithRetry();
}

void KMBoxController::Disconnect() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 停止监控
    StopMonitoring();
    
    m_connectionStatus = ConnectionStatus::Disconnected;
    std::cout << "KMBoxNet设备已断开连接" << std::endl;
}

MouseController::ConnectionStatus KMBoxController::GetConnectionStatus() const {
    return m_connectionStatus.load();
}

bool KMBoxController::IsConnected() const {
    return m_connectionStatus.load() == ConnectionStatus::Connected;
}

// ==================== 核心功能1：状态查询（实现抽象接口） ====================

MouseController::MouseState KMBoxController::GetMouseState() {
    MouseState state;
    
    if (!IsConnected()) {
        return state;
    }
    
    // 查询按键状态
    state.leftButton = (kmNet_monitor_mouse_left() == 1);
    state.rightButton = (kmNet_monitor_mouse_right() == 1);
    state.middleButton = (kmNet_monitor_mouse_middle() == 1);
    state.side1Button = (kmNet_monitor_mouse_side1() == 1);
    state.side2Button = (kmNet_monitor_mouse_side2() == 1);
    
    // 查询鼠标位置
    kmNet_monitor_mouse_xy(&state.x, &state.y);
    
    // 查询滚轮状态
    kmNet_monitor_mouse_wheel(&state.wheel);
    
    return state;
}

bool KMBoxController::IsMouseButtonPressed(MouseButton button) {
    if (!IsConnected()) {
        return false;
    }
    
    switch (button) {
        case MouseButton::Left:
            return kmNet_monitor_mouse_left() == 1;
        case MouseButton::Right:
            return kmNet_monitor_mouse_right() == 1;
        case MouseButton::Middle:
            return kmNet_monitor_mouse_middle() == 1;
        case MouseButton::Side1:
            return kmNet_monitor_mouse_side1() == 1;
        case MouseButton::Side2:
            return kmNet_monitor_mouse_side2() == 1;
        default:
            return false;
    }
}

bool KMBoxController::StartMonitoring(MouseEventCallback callback) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    if (m_monitoring.load()) {
        std::cout << "鼠标监控已经在运行中" << std::endl;
        return true;
    }
    
    // 启动KMBoxNet监控
    int result = kmNet_monitor(static_cast<short>(m_config.monitorPort));
    if (result != 0) {
        HandleError(result, "启动监控失败: " + GetErrorMessage(result));
        return false;
    }
    
    m_mouseEventCallback = callback;
    m_monitoring = true;
    
    // 启动监控线程
    m_monitorThread = std::make_unique<std::thread>(&KMBoxController::MonitorThreadFunction, this);
    
    std::cout << "鼠标监控已启动，监听端口: " << m_config.monitorPort << std::endl;
    return true;
}

void KMBoxController::StopMonitoring() {
    if (!m_monitoring.load()) {
        return;
    }
    
    m_monitoring = false;
    
    // 等待监控线程结束
    if (m_monitorThread && m_monitorThread->joinable()) {
        m_monitorThread->join();
        m_monitorThread.reset();
    }
    
    std::cout << "鼠标监控已停止" << std::endl;
}

// ==================== 核心功能2：移动执行（实现抽象接口） ====================

bool KMBoxController::MoveMouse(int deltaX, int deltaY, MoveType moveType, int duration) {
    switch (moveType) {
        case MoveType::Instant:
            return MoveMouse(deltaX, deltaY);
        case MoveType::Smooth:
            return SmoothMoveMouse(deltaX, deltaY, duration > 0 ? duration : 500);
        case MoveType::Bezier:
            // 使用默认控制点进行贝塞尔移动
            return BezierMoveMouse(deltaX, deltaY, duration > 0 ? duration : 500, 
                                 deltaX / 3, deltaY / 3, deltaX * 2 / 3, deltaY * 2 / 3);
        default:
            return MoveMouse(deltaX, deltaY);
    }
}

bool KMBoxController::MoveMouse(int deltaX, int deltaY) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [this, deltaX, deltaY]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move(static_cast<short>(deltaX), static_cast<short>(deltaY));
        } else {
            return kmNet_mouse_move(static_cast<short>(deltaX), static_cast<short>(deltaY));
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::SmoothMoveMouse(int deltaX, int deltaY, int duration) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [this, deltaX, deltaY, duration]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move_auto(deltaX, deltaY, duration);
        } else {
            return kmNet_mouse_move_auto(deltaX, deltaY, duration);
        }
    };
    
    return ExecuteWithRetry(operation);
}

bool KMBoxController::BezierMoveMouse(int deltaX, int deltaY, int duration, 
                                     int controlX1, int controlY1, int controlX2, int controlY2) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }
    
    auto operation = [=]() -> int {
        if (m_config.enableEncryption) {
            return kmNet_enc_mouse_move_beizer(deltaX, deltaY, duration, controlX1, controlY1, controlX2, controlY2);
        } else {
            return kmNet_mouse_move_beizer(deltaX, deltaY, duration, controlX1, controlY1, controlX2, controlY2);
        }
    };
    
    return ExecuteWithRetry(operation);
}

// ==================== 配置和回调（实现抽象接口） ====================

void KMBoxController::SetErrorCallback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_errorCallback = callback;
}

int KMBoxController::GetLastError() const {
    return m_lastError.load();
}

std::string KMBoxController::GetLastErrorMessage() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_lastErrorMessage;
}

std::string KMBoxController::GetControllerType() const {
    return "KMBoxNet";
}

std::string KMBoxController::GetVersion() const {
    return "1.0.0";
}

// ==================== 辅助功能（实现抽象接口） ====================

bool KMBoxController::PressMouseButton(MouseButton button) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }

    auto operation = [this, button]() -> int {
        switch (button) {
            case MouseButton::Left:
                return m_config.enableEncryption ? kmNet_enc_mouse_left(1) : kmNet_mouse_left(1);
            case MouseButton::Right:
                return m_config.enableEncryption ? kmNet_enc_mouse_right(1) : kmNet_mouse_right(1);
            case MouseButton::Middle:
                return m_config.enableEncryption ? kmNet_enc_mouse_middle(1) : kmNet_mouse_middle(1);
            case MouseButton::Side1:
                return m_config.enableEncryption ? kmNet_enc_mouse_side1(1) : kmNet_mouse_side1(1);
            case MouseButton::Side2:
                return m_config.enableEncryption ? kmNet_enc_mouse_side2(1) : kmNet_mouse_side2(1);
            default:
                return -1;
        }
    };

    return ExecuteWithRetry(operation);
}

bool KMBoxController::ReleaseMouseButton(MouseButton button) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }

    auto operation = [this, button]() -> int {
        switch (button) {
            case MouseButton::Left:
                return m_config.enableEncryption ? kmNet_enc_mouse_left(0) : kmNet_mouse_left(0);
            case MouseButton::Right:
                return m_config.enableEncryption ? kmNet_enc_mouse_right(0) : kmNet_mouse_right(0);
            case MouseButton::Middle:
                return m_config.enableEncryption ? kmNet_enc_mouse_middle(0) : kmNet_mouse_middle(0);
            case MouseButton::Side1:
                return m_config.enableEncryption ? kmNet_enc_mouse_side1(0) : kmNet_mouse_side1(0);
            case MouseButton::Side2:
                return m_config.enableEncryption ? kmNet_enc_mouse_side2(0) : kmNet_mouse_side2(0);
            default:
                return -1;
        }
    };

    return ExecuteWithRetry(operation);
}

bool KMBoxController::ClickMouseButton(MouseButton button, int holdTime) {
    if (!PressMouseButton(button)) {
        return false;
    }

    Sleep(holdTime);

    return ReleaseMouseButton(button);
}

bool KMBoxController::ScrollWheel(int direction, int steps) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }

    int wheelValue = (direction > 0) ? 3 : -3;

    for (int i = 0; i < steps; i++) {
        auto operation = [this, wheelValue]() -> int {
            return m_config.enableEncryption ? kmNet_enc_mouse_wheel(wheelValue) : kmNet_mouse_wheel(wheelValue);
        };

        if (!ExecuteWithRetry(operation)) {
            return false;
        }

        Sleep(50); // 短暂延迟避免过快滚动
    }

    return true;
}

// ==================== KMBox特有功能 ====================

bool KMBoxController::DragMouse(int startX, int startY, int endX, int endY, int duration) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }

    // 移动到起始位置
    if (!MoveMouse(startX, startY)) {
        return false;
    }
    Sleep(100);

    // 按下左键
    if (!PressMouseButton(MouseButton::Left)) {
        return false;
    }
    Sleep(50);

    // 拖拽到目标位置
    if (!SmoothMoveMouse(endX - startX, endY - startY, duration)) {
        ReleaseMouseButton(MouseButton::Left); // 确保释放按键
        return false;
    }
    Sleep(100);

    // 松开左键
    return ReleaseMouseButton(MouseButton::Left);
}

bool KMBoxController::DrawCircle(int radius, int steps, bool clockwise) {
    if (!IsConnected()) {
        HandleError(-1, "设备未连接");
        return false;
    }

    const double PI = 3.141592654;
    double angleStep = 2.0 * PI / steps;
    if (!clockwise) {
        angleStep = -angleStep;
    }

    int prevX = 0, prevY = 0;

    for (int i = 0; i < steps; i++) {
        double angle = i * angleStep;
        int x = static_cast<int>(radius * cos(angle));
        int y = static_cast<int>(radius * sin(angle));

        if (i == 0) {
            // 移动到起始点
            if (!MoveMouse(x, y)) {
                return false;
            }
        } else {
            // 相对移动
            if (!MoveMouse(x - prevX, y - prevY)) {
                return false;
            }
        }

        prevX = x;
        prevY = y;
        Sleep(10); // 短暂延迟使轨迹更平滑
    }

    return true;
}

// ==================== 私有方法实现 ====================

bool KMBoxController::ConnectWithRetry() {
    for (int attempt = 0; attempt < m_config.maxRetries; attempt++) {
        std::cout << "连接尝试 " << (attempt + 1) << "/" << m_config.maxRetries << std::endl;

        int result = kmNet_init(
            const_cast<char*>(m_config.ip.c_str()),
            const_cast<char*>(m_config.port.c_str()),
            const_cast<char*>(m_config.mac.c_str())
        );

        if (result == 0) {
            m_connectionStatus = ConnectionStatus::Connected;
            m_lastError = 0;
            m_lastErrorMessage = "";
            std::cout << "KMBoxNet连接成功！" << std::endl;
            return true;
        }

        std::string errorMsg = "连接失败，错误码: " + std::to_string(result) + " - " + GetErrorMessage(result);
        std::cout << errorMsg << std::endl;

        if (attempt < m_config.maxRetries - 1) {
            std::cout << "等待 " << m_config.retryDelayMs << "ms 后重试..." << std::endl;
            Sleep(m_config.retryDelayMs);
        }
    }

    m_connectionStatus = ConnectionStatus::Error;
    HandleError(-1, "连接失败，已达到最大重试次数");
    return false;
}

void KMBoxController::HandleError(int errorCode, const std::string& message) {
    m_lastError = errorCode;
    m_lastErrorMessage = message;

    if (m_errorCallback) {
        m_errorCallback(errorCode, message);
    }

    std::cerr << "KMBoxController错误: " << message << " (错误码: " << errorCode << ")" << std::endl;
}

void KMBoxController::MonitorThreadFunction() {
    std::cout << "鼠标监控线程已启动" << std::endl;

    MouseState lastState;

    while (m_monitoring.load()) {
        try {
            MouseState currentState = GetMouseState();

            // 检查状态是否发生变化
            bool stateChanged = false;
            if (currentState.leftButton != lastState.leftButton ||
                currentState.rightButton != lastState.rightButton ||
                currentState.middleButton != lastState.middleButton ||
                currentState.side1Button != lastState.side1Button ||
                currentState.side2Button != lastState.side2Button ||
                currentState.x != lastState.x ||
                currentState.y != lastState.y ||
                currentState.wheel != lastState.wheel) {
                stateChanged = true;
            }

            // 如果状态发生变化且有回调函数，则调用回调
            if (stateChanged && m_mouseEventCallback) {
                m_mouseEventCallback(currentState);
            }

            lastState = currentState;

        } catch (const std::exception& e) {
            HandleError(-1, "监控线程异常: " + std::string(e.what()));
        }

        Sleep(10); // 避免CPU占用过高，每10ms检查一次
    }

    std::cout << "鼠标监控线程已结束" << std::endl;
}

bool KMBoxController::ExecuteWithRetry(std::function<int()> operation, int maxRetries) {
    for (int attempt = 0; attempt < maxRetries; attempt++) {
        int result = operation();

        if (result == 0) {
            return true;
        }

        // 如果是网络错误，尝试重连
        if (result == ERR_NET_TX || result == ERR_NET_RX_TIMEOUT) {
            std::cout << "网络错误，尝试重连..." << std::endl;
            if (ConnectWithRetry()) {
                continue; // 重连成功，重试操作
            }
        }

        HandleError(result, GetErrorMessage(result));
    }

    return false;
}

std::string KMBoxController::GetErrorMessage(int errorCode) const {
    switch (errorCode) {
        case 0: return "成功";
        case ERR_CREAT_SOCKET: return "创建socket失败";
        case ERR_NET_VERSION: return "socket版本错误";
        case ERR_NET_TX: return "socket发送错误";
        case ERR_NET_RX_TIMEOUT: return "socket接收超时";
        case ERR_NET_CMD: return "命令错误";
        case ERR_NET_PTS: return "时间戳错误";
        default: return "未知错误";
    }
}

KMBoxController::KMBoxConfig KMBoxController::ParseConfigJson(const std::string& configJson) {
    KMBoxConfig config;

    if (configJson.empty()) {
        return config; // 返回默认配置
    }

    // 简单的JSON解析（仅支持基本格式）
    // 这里为了简化，暂时返回默认配置
    // 在实际项目中可以使用专门的JSON库或实现更完整的解析

    std::cout << "使用默认KMBox配置（JSON解析功能待完善）" << std::endl;

    return config;
}
