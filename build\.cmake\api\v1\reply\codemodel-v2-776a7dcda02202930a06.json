{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-b32661512a76d834e7b0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Debug-3466d9c3846406b170dd.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-Debug-46c8366a670ae285d632.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Debug-47ab4d333dc4c9d24292.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Debug-54d835f1152cb8c3f480.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-Debug-c60299c990e32b9c5965.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-b32661512a76d834e7b0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Release-39d3090409a2512ae4d7.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-Release-5df5167e4fbcd67f7b08.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Release-f2232bb331f791d0ddc6.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-Release-0ec0036c4ca2072441bf.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-Release-613883869cdd17bcd34e.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-b32661512a76d834e7b0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-MinSizeRel-3786e1f2e2f350e91c34.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-MinSizeRel-b8e14f73d35f5f14c097.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-MinSizeRel-4677b939ea5be5bbf577.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-MinSizeRel-10689cb10fa2879d7a10.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-MinSizeRel-ceca8012750a7ccee457.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b32661512a76d834e7b0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-RelWithDebInfo-cd1a699ca8bdab44e10d.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimSystemIntegration::@6890427a1f51a3e7e1df", "jsonFile": "target-AimSystemIntegration-RelWithDebInfo-f40e22f0af8082b2a6b9.json", "name": "AimSystemIntegration", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-RelWithDebInfo-c98787104645b523b485.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "MouseControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-MouseControllerTest-RelWithDebInfo-70a89d9206bd258236aa.json", "name": "MouseControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "PIDControllerTest::@6890427a1f51a3e7e1df", "jsonFile": "target-PIDControllerTest-RelWithDebInfo-42a727406ac04d226c67.json", "name": "PIDControllerTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/aim_bot/build", "source": "C:/Users/<USER>/Desktop/aim_bot"}, "version": {"major": 2, "minor": 4}}