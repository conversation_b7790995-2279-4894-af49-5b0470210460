#include "autonomous_move_thread.h"
#include <iostream>

AutonomousMoveThread::AutonomousMoveThread(std::shared_ptr<MouseController> mouseController, 
                                         const PIDController::PIDConfig& pidConfig)
    : m_mouse<PERSON>ontroller(mouseController)
    , m_pidController(std::make_unique<PIDController>(pidConfig))
    , m_startTime(std::chrono::high_resolution_clock::now())
    , m_lastMoveTime(std::chrono::high_resolution_clock::now()) {
    
    if (!m_mouseController) {
        throw std::invalid_argument("MouseController???????");
    }
    
    std::cout << "???????????????????????: " << m_moveFrequency.load() << "Hz" << std::endl;
}

AutonomousMoveThread::~AutonomousMoveThread() {
    Stop();
}

void AutonomousMoveThread::Start() {
    if (m_running.load()) {
        std::cout << "?????????????????????" << std::endl;
        return;
    }
    
    m_shouldStop.store(false);
    m_running.store(true);
    m_startTime = std::chrono::high_resolution_clock::now();
    
    m_moveThread = std::thread(&AutonomousMoveThread::AutonomousMoveLoop, this);
    std::cout << "????????????????" << std::endl;
}

void AutonomousMoveThread::Stop() {
    if (!m_running.load()) {
        return;
    }
    
    std::cout << "????????????????..." << std::endl;
    
    m_shouldStop.store(true);
    m_wakeupCondition.notify_all();
    
    if (m_moveThread.joinable()) {
        m_moveThread.join();
    }
    
    m_running.store(false);
    std::cout << "??????????????" << std::endl;
}

void AutonomousMoveThread::UpdateTarget(const Vector2D& targetPosition) {
    std::lock_guard<std::mutex> lock(m_targetMutex);
    m_targetState = TargetState(targetPosition);
    
    // ??????????????????????
    m_wakeupCondition.notify_one();
}

void AutonomousMoveThread::ClearTarget() {
    std::lock_guard<std::mutex> targetLock(m_targetMutex);
    m_targetState.isValid = false;

    // ????PID????????
    m_pidController->Reset();

    // ???????��??
    std::lock_guard<std::mutex> positionLock(m_positionMutex);
    m_currentAimPosition = Vector2D(0.0f, 0.0f);
    m_lastCalculatedError = Vector2D(0.0f, 0.0f);
}

void AutonomousMoveThread::ResetAimPosition() {
    std::lock_guard<std::mutex> lock(m_positionMutex);
    m_currentAimPosition = Vector2D(0.0f, 0.0f);
    m_lastCalculatedError = Vector2D(0.0f, 0.0f);

    // ??????PID??
    m_pidController->Reset();
}

void AutonomousMoveThread::UpdatePIDConfig(const PIDController::PIDConfig& config) {
    m_pidController->UpdateConfig(config);
}

PIDController::PIDConfig AutonomousMoveThread::GetPIDConfig() const {
    return m_pidController->GetConfig();
}

PIDController::DebugInfo AutonomousMoveThread::GetPIDDebugInfo() const {
    return m_pidController->GetDebugInfo();
}

AutonomousMoveThread::MoveThreadStats AutonomousMoveThread::GetStats() const {
    std::lock_guard<std::mutex> targetLock(m_targetMutex);
    std::lock_guard<std::mutex> positionLock(m_positionMutex);
    std::lock_guard<std::mutex> statsLock(m_statsMutex);

    MoveThreadStats stats;
    stats.isRunning = m_running.load();
    stats.hasTarget = m_targetState.isValid && IsTargetValid();
    stats.currentTarget = m_targetState.position;
    stats.currentAimPosition = m_currentAimPosition;
    stats.lastCalculatedError = m_lastCalculatedError;
    stats.lastMoveVector = m_lastMoveVector;
    stats.totalMoves = m_totalMoves;
    stats.avgMoveTime = m_totalMoves > 0 ? m_totalMoveTime / m_totalMoves : 0.0;
    
    // ??????????
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
    stats.moveFrequency = elapsed.count() > 0 ? (m_totalMoves * 1000.0) / elapsed.count() : 0.0;
    
    // ???????????
    stats.targetAge = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_targetState.timestamp);
    
    return stats;
}

void AutonomousMoveThread::SetMoveFrequency(double frequency) {
    m_moveFrequency.store(frequency);
    std::cout << "???????????: " << frequency << "Hz" << std::endl;
}

void AutonomousMoveThread::SetTargetTimeout(int timeoutMs) {
    m_targetTimeoutMs.store(timeoutMs);
    std::cout << "????????????: " << timeoutMs << "ms" << std::endl;
}

void AutonomousMoveThread::AutonomousMoveLoop() {
    std::cout << "?????????????????????: " << m_moveFrequency.load() << "Hz" << std::endl;
    
    auto lastLoopTime = std::chrono::high_resolution_clock::now();
    
    while (!m_shouldStop.load()) {
        auto loopStartTime = std::chrono::high_resolution_clock::now();
        
        // ?????????????
        double frequency = m_moveFrequency.load();
        auto targetInterval = std::chrono::microseconds(static_cast<long long>(1000000.0 / frequency));
        
        // ???????????????
        bool hasValidTarget = false;
        {
            std::lock_guard<std::mutex> lock(m_targetMutex);
            hasValidTarget = m_targetState.isValid && IsTargetValid();
        }
        
        if (hasValidTarget) {
            // ???PID????????
            PerformMove();
        } else {
            // ????????????????????
            std::unique_lock<std::mutex> lock(m_wakeupMutex);
            m_wakeupCondition.wait_for(lock, std::chrono::milliseconds(50), [this] {
                return m_shouldStop.load() || (m_targetState.isValid && IsTargetValid());
            });
        }
        
        // ??????????
        auto loopEndTime = std::chrono::high_resolution_clock::now();
        auto elapsed = loopEndTime - loopStartTime;
        
        if (elapsed < targetInterval) {
            std::this_thread::sleep_for(targetInterval - elapsed);
        }
        
        lastLoopTime = loopEndTime;
    }
    
    std::cout << "?????????????????" << std::endl;
}

void AutonomousMoveThread::PerformMove() {
    if (!m_mouseController || !m_mouseController->IsConnected()) {
        return;
    }
    
    auto moveStartTime = std::chrono::high_resolution_clock::now();
    
    try {
        // 🎯 使用智能误差选择：优先使用最新目标，否则使用自维护误差
        Vector2D currentError = CalculateCurrentError();

        // ʹ�õ�ǰ������PID����
        Vector2D moveVector = m_pidController->Calculate(currentError);
        
        // ??????????????????????
        if (moveVector.Length() > 0.5f) {
            m_mouseController->MoveMouse(
                static_cast<int>(moveVector.x),
                static_cast<int>(moveVector.y),
                MouseController::MoveType::Smooth,
                5  // 5ms????????????????
            );

            // ???????�s?????????��???
            {
                std::lock_guard<std::mutex> positionLock(m_positionMutex);
                m_currentAimPosition += moveVector;  // ??????????
                m_lastCalculatedError = currentError;
            }

            // ??????????
            auto moveEndTime = std::chrono::high_resolution_clock::now();
            auto moveDuration = std::chrono::duration_cast<std::chrono::microseconds>(moveEndTime - moveStartTime);
            double moveTime = moveDuration.count() / 1000.0; // ????????

            std::lock_guard<std::mutex> statsLock(m_statsMutex);
            m_totalMoves++;
            m_totalMoveTime += moveTime;
            m_lastMoveVector = moveVector;
            m_lastMoveTime = moveEndTime;
        } else {
            // ??????????????????????
            std::lock_guard<std::mutex> positionLock(m_positionMutex);
            m_lastCalculatedError = currentError;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "?????????????: " << e.what() << std::endl;
    }
}

bool AutonomousMoveThread::IsTargetValid() const {
    auto now = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_targetState.timestamp);
    return elapsed.count() < m_targetTimeoutMs.load();
}

Vector2D AutonomousMoveThread::CalculateCurrentError() {
    Vector2D currentError;
    Vector2D currentAimPosition;
    bool hasLatestTarget = false;

    // ��ȡ��ǰ��׼λ��
    {
        std::lock_guard<std::mutex> positionLock(m_positionMutex);
        currentAimPosition = m_currentAimPosition;
    }

    // ? ���ȼ���Ƿ��������̵߳�����Ŀ��
    {
        std::lock_guard<std::mutex> targetLock(m_targetMutex);
        if (m_targetState.isValid && IsTargetValid()) {
            // ������Ŀ�꣬���������������
            Vector2D currentScreenPosition = m_screenCenter + currentAimPosition;
            currentError = m_targetState.position - currentScreenPosition;
            hasLatestTarget = true;
        }
    }

    // ���û������Ŀ�꣬ʹ����ά��������������
    if (!hasLatestTarget) {
        std::lock_guard<std::mutex> positionLock(m_positionMutex);
        // ʹ���ϴε�������˥���Ա��������ƶ�
        currentError = m_lastCalculatedError;  // 不衰减，让PID控制器自己处理收敛
    }

    return currentError;
}
