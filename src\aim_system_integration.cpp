#include <iostream>
#include <vector>
#include <Windows.h>
#include "mouse_controller.h"
#include "pid_controller.h"

/**
 * @brief 自瞄系统集成示例
 * 
 * 这个程序展示了如何将PID控制器与鼠标控制器结合，
 * 实现完整的自瞄系统控制逻辑。
 */

// 模拟AI检测结果结构体
struct Detection {
    float x, y;           // 目标中心位置
    float width, height;  // 目标尺寸
    float confidence;     // 置信度
    int classId;          // 类别ID (0=头部, 1=身体)
};

// 简单的自瞄系统类
class SimpleAimSystem {
private:
    std::unique_ptr<MouseController> m_mouseController;
    std::unique_ptr<PIDController> m_pidController;
    
    // 屏幕中心点（准星位置）
    Vector2D m_screenCenter;
    
    // 自瞄配置
    bool m_enabled = false;
    bool m_autoFire = false;
    float m_aimFOV = 100.0f;        // 自瞄视野范围
    float m_headPriority = 1.5f;    // 头部优先级
    
public:
    SimpleAimSystem(Vector2D screenCenter = Vector2D(160.0f, 160.0f)) 
        : m_screenCenter(screenCenter) {}
    
    bool Initialize(const std::string& mouseControllerType = "kmbox") {
        std::cout << "正在初始化自瞄系统..." << std::endl;
        
        // 创建鼠标控制器
        m_mouseController = MouseControllerFactory::Create(mouseControllerType);
        if (!m_mouseController) {
            std::cout << "鼠标控制器创建失败" << std::endl;
            return false;
        }
        
        // 初始化鼠标控制器
        std::string config = R"({
            "ip": "*************",
            "port": "8808",
            "mac": "62587019"
        })";
        
        if (!m_mouseController->Initialize(config)) {
            std::cout << "鼠标控制器初始化失败" << std::endl;
            return false;
        }
        
        // 创建PID控制器
        m_pidController = std::make_unique<PIDController>(
            PIDControllerFactory::CreateAdaptiveResponse()
        );
        
        // 设置目标到达回调
        m_pidController->SetTargetReachedCallback([this]() {
            if (m_autoFire) {
                m_mouseController->ClickMouseButton(MouseController::MouseButton::Left, 50);
                std::cout << "? 自动开火！" << std::endl;
            }
        });
        
        std::cout << "自瞄系统初始化成功" << std::endl;
        return true;
    }
    
    void SetEnabled(bool enabled) {
        m_enabled = enabled;
        if (enabled) {
            std::cout << "? 自瞄已启用" << std::endl;
        } else {
            std::cout << "? 自瞄已禁用" << std::endl;
            m_pidController->Reset();
        }
    }
    
    void SetAutoFire(bool autoFire) {
        m_autoFire = autoFire;
        std::cout << "自动开火: " << (autoFire ? "启用" : "禁用") << std::endl;
    }
    
    bool IsEnabled() const { return m_enabled; }
    
    void Update(const std::vector<Detection>& detections) {
        if (!m_enabled || !m_mouseController || !m_mouseController->IsConnected()) {
            return;
        }
        
        // 检查用户输入（右键启动瞄准）
        if (!m_mouseController->IsMouseButtonPressed(MouseController::MouseButton::Right)) {
            return;
        }
        
        // 选择最佳目标
        Detection bestTarget;
        if (!SelectBestTarget(detections, bestTarget)) {
            return; // 没有有效目标
        }
        
        // 计算误差（目标位置 - 准星位置）
        Vector2D targetPos(bestTarget.x + bestTarget.width / 2.0f, 
                          bestTarget.y + bestTarget.height / 2.0f);
        Vector2D error = targetPos - m_screenCenter;
        
        // 使用PID控制器计算移动向量
        Vector2D moveVector = m_pidController->Calculate(error);
        
        // 执行鼠标移动
        if (moveVector.Length() > 0.5f) {
            m_mouseController->MoveMouse(
                static_cast<int>(moveVector.x), 
                static_cast<int>(moveVector.y),
                MouseController::MoveType::Smooth,
                100
            );
            
            // 显示调试信息
            auto debug = m_pidController->GetDebugInfo();
            std::cout << "目标: (" << targetPos.x << ", " << targetPos.y 
                     << ") 误差: " << error.Length() 
                     << " 输出: " << moveVector.Length() << std::endl;
        }
    }
    
private:
    bool SelectBestTarget(const std::vector<Detection>& detections, Detection& bestTarget) {
        if (detections.empty()) {
            return false;
        }
        
        float bestScore = -1.0f;
        bool found = false;
        
        for (const auto& detection : detections) {
            // 计算目标中心点
            Vector2D targetCenter(detection.x + detection.width / 2.0f,
                                 detection.y + detection.height / 2.0f);
            
            // 计算距离准星的距离
            float distance = (targetCenter - m_screenCenter).Length();
            
            // 检查是否在自瞄视野范围内
            if (distance > m_aimFOV) {
                continue;
            }
            
            // 计算目标优先级分数
            float score = detection.confidence;
            
            // 头部目标优先级更高
            if (detection.classId == 0) {
                score *= m_headPriority;
            }
            
            // 距离越近优先级越高
            score *= (m_aimFOV - distance) / m_aimFOV;
            
            if (score > bestScore) {
                bestScore = score;
                bestTarget = detection;
                found = true;
            }
        }
        
        return found;
    }
};

// 模拟AI检测结果
std::vector<Detection> SimulateDetections() {
    static int frame = 0;
    frame++;
    
    std::vector<Detection> detections;
    
    // 每30帧生成一个新的检测结果
    if (frame % 30 == 0) {
        Detection detection;
        detection.x = 120 + (rand() % 80);      // 在屏幕中心附近
        detection.y = 120 + (rand() % 80);
        detection.width = 15 + (rand() % 10);
        detection.height = 20 + (rand() % 10);
        detection.confidence = 0.7f + (rand() % 30) / 100.0f;
        detection.classId = rand() % 2;  // 0=头部, 1=身体
        
        detections.push_back(detection);
        
        std::cout << "检测到目标: (" << detection.x << ", " << detection.y 
                 << ") 类型: " << (detection.classId == 0 ? "头部" : "身体")
                 << " 置信度: " << detection.confidence << std::endl;
    }
    
    return detections;
}

void PrintControls() {
    std::cout << "\n=== 控制说明 ===" << std::endl;
    std::cout << "F1 - 启用/禁用自瞄" << std::endl;
    std::cout << "F2 - 启用/禁用自动开火" << std::endl;
    std::cout << "F3 - 切换PID模式" << std::endl;
    std::cout << "右键 - 激活瞄准（需要按住）" << std::endl;
    std::cout << "ESC - 退出程序" << std::endl;
    std::cout << "================" << std::endl;
}

int main() {
    std::cout << "=== 自瞄系统集成示例 ===" << std::endl;
    std::cout << "这个程序展示了PID控制器与鼠标控制器的完整集成" << std::endl;
    
    // 创建自瞄系统
    SimpleAimSystem aimSystem(Vector2D(160.0f, 160.0f));  // 假设320x320屏幕的中心
    
    if (!aimSystem.Initialize("kmbox")) {
        std::cout << "自瞄系统初始化失败" << std::endl;
        std::cout << "按Enter键退出...";
        std::cin.get();
        return -1;
    }
    
    PrintControls();
    
    std::cout << "\n开始运行自瞄系统..." << std::endl;
    std::cout << "请按F1启用自瞄，然后按住右键进行瞄准测试" << std::endl;
    
    int frameCount = 0;
    int pidMode = 0;  // 0=自适应, 1=快速, 2=平滑, 3=精确
    
    while (true) {
        frameCount++;
        
        // 检查退出
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
        
        // 检查F1键 - 启用/禁用自瞄
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            aimSystem.SetEnabled(!aimSystem.IsEnabled());
            Sleep(200); // 防止重复触发
        }
        
        // 检查F2键 - 启用/禁用自动开火
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            static bool autoFire = false;
            autoFire = !autoFire;
            aimSystem.SetAutoFire(autoFire);
            Sleep(200);
        }
        
        // 检查F3键 - 切换PID模式
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            pidMode = (pidMode + 1) % 4;
            std::cout << "切换PID模式: ";
            switch (pidMode) {
                case 0: std::cout << "自适应响应"; break;
                case 1: std::cout << "快速响应"; break;
                case 2: std::cout << "平滑响应"; break;
                case 3: std::cout << "精确响应"; break;
            }
            std::cout << std::endl;
            Sleep(200);
        }
        
        // 模拟AI检测
        auto detections = SimulateDetections();
        
        // 更新自瞄系统
        aimSystem.Update(detections);
        
        // 显示状态信息（每60帧一次）
        if (frameCount % 60 == 0) {
            std::cout << "\n--- 状态信息 ---" << std::endl;
            std::cout << "帧数: " << frameCount << std::endl;
            std::cout << "自瞄状态: " << (aimSystem.IsEnabled() ? "启用" : "禁用") << std::endl;
            std::cout << "检测数量: " << detections.size() << std::endl;
            std::cout << "---------------" << std::endl;
        }
        
        // 控制帧率（约60FPS）
        Sleep(16);
    }
    
    std::cout << "\n正在退出自瞄系统..." << std::endl;
    std::cout << "程序已退出" << std::endl;
    
    return 0;
}
