# AimBot - 实时AI自瞄系统

## 项目概述

这是一个基于AI视觉识别和硬件控制的实时自瞄系统，主要用于游戏辅助。系统通过实时屏幕捕获、AI目标检测和精确的鼠标控制来实现自动瞄准功能。

## 核心特性

- **实时屏幕捕获**: 使用DirectX 11高性能捕获屏幕中心区域
- **GPU加速预处理**: 利用GPU计算着色器进行图像预处理
- **AI目标检测**: 基于ONNX Runtime的实时目标检测（头部、身体）
- **硬件级鼠标控制**: 通过KMBoxNet硬件实现精确的鼠标移动控制
- **低延迟设计**: 全流程GPU加速，实现毫秒级响应

## 系统架构

```
屏幕捕获 -> GPU预处理 -> AI推理 -> 目标跟踪 -> 鼠标控制
    ↓           ↓          ↓         ↓         ↓
ScreenCapture -> GPUPreprocessor -> InferenceEngine -> AimController -> KMBoxController
```

### 核心模块

1. **ScreenCapture** - 屏幕捕获模块
   - 使用Windows Desktop Duplication API
   - 支持指定区域捕获（默认中心320x320）
   - GPU纹理直接传递，避免CPU-GPU数据拷贝

2. **GPUPreprocessor** - GPU预处理模块
   - HLSL计算着色器实现图像预处理
   - 格式转换（BGRA -> RGB）
   - 归一化和标准化处理

3. **InferenceEngine** - AI推理引擎
   - 基于ONNX Runtime DirectML
   - 支持YOLO系列目标检测模型
   - GPU加速推理，支持批处理

4. **KMBoxController** - 鼠标控制模块（本次开发重点）
   - 基于KMBoxNet硬件的鼠标控制
   - 支持精确的相对移动和绝对定位
   - 鼠标按键监控和控制
   - 平滑轨迹生成

5. **AimController** - 自瞄控制模块（待开发）
   - 目标选择和优先级算法
   - PID控制器实现平滑跟踪
   - 预测算法补偿延迟

## 技术栈

- **语言**: C++17
- **图形API**: DirectX 11
- **AI框架**: ONNX Runtime DirectML
- **硬件控制**: KMBoxNet
- **构建系统**: CMake
- **依赖管理**: vcpkg

## 硬件要求

- **显卡**: 支持DirectX 11的GPU（推荐GTX 1060或更高）
- **内存**: 8GB RAM（推荐16GB）
- **网络硬件**: KMBoxNet设备
- **操作系统**: Windows 10/11 64位

## KMBoxNet配置

KMBoxNet是一个网络硬件鼠标模拟器，需要正确配置网络连接：

- **设备IP**: 通常为*************
- **端口**: 8808
- **MAC地址**: 设备显示屏上显示的MAC地址

## 编译和运行

### 环境准备

1. 安装Visual Studio 2019/2022（包含C++工具链）
2. 安装CMake 3.20+
3. 确保KMBoxNet设备正确连接并配置

### 编译步骤

```bash
# 创建构建目录
mkdir build
cd build

# 生成项目文件
cmake ..

# 编译项目
cmake --build . --config Release
```

### 运行程序

```bash
# 运行主程序
./Release/AimBot.exe
```

程序提供两种测试模式：
1. 简单屏幕捕获测试（不包含AI推理）
2. 实时AI推理测试（包含目标检测显示）

## 项目结构

```
aim_bot/
├── src/                    # 源代码
│   ├── main.cpp           # 主程序入口
│   ├── screen_capture.cpp # 屏幕捕获实现
│   ├── gpu_preprocessor.cpp # GPU预处理实现
│   ├── inference_engine.cpp # AI推理引擎实现
│   ├── gpu_renderer.cpp   # GPU渲染器实现
│   └── kmbox/            # KMBoxNet相关文件
├── include/               # 头文件
├── shaders/              # HLSL着色器文件
├── models/               # AI模型文件
├── python_aim/           # Python参考实现
└── NetConfig/            # KMBoxNet配置文件
```

## 开发计划

### 当前阶段：鼠标控制模块开发
- [x] 理解KMBoxNet API
- [ ] 设计KMBoxController类
- [ ] 实现设备连接和管理
- [ ] 实现鼠标移动控制
- [ ] 实现鼠标按键监控
- [ ] 集成到主程序流程

### 下一阶段：自瞄控制模块
- [ ] 设计AimController类
- [ ] 实现目标选择算法
- [ ] 实现PID控制器
- [ ] 实现轨迹平滑算法
- [ ] 性能优化和调试

## 安全和合规性

⚠️ **重要提醒**: 
- 本项目仅用于技术学习和研究目的
- 请遵守相关游戏的使用条款和当地法律法规
- 不建议在正式游戏环境中使用
- 开发者不承担任何滥用责任

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
