#include "mouse_controller.h"
#include "kmbox_controller_new.h"
#include <iostream>
#include <algorithm>
#include <cctype>

// 前向声明其他控制器类（暂时未实现）
// class SystemMouseController;
// class LogitechMouseController;
// class RazerMouseController;
// class MockMouseController;

std::unique_ptr<MouseController> MouseControllerFactory::Create(MouseControllerType type) {
    switch (type) {
        case MouseControllerType::KMBox:
            return std::make_unique<KMBoxController>();
            
        case MouseControllerType::System:
            std::cout << "系统鼠标控制器暂未实现" << std::endl;
            return nullptr;
            
        case MouseControllerType::Logitech:
            std::cout << "罗技鼠标控制器暂未实现" << std::endl;
            return nullptr;
            
        case MouseControllerType::Razer:
            std::cout << "雷蛇鼠标控制器暂未实现" << std::endl;
            return nullptr;
            
        case MouseControllerType::Mock:
            std::cout << "模拟鼠标控制器暂未实现" << std::endl;
            return nullptr;
            
        default:
            std::cout << "未知的鼠标控制器类型" << std::endl;
            return nullptr;
    }
}

std::unique_ptr<MouseController> MouseControllerFactory::Create(const std::string& typeName) {
    // 转换为小写进行比较
    std::string lowerTypeName = typeName;
    std::transform(lowerTypeName.begin(), lowerTypeName.end(), lowerTypeName.begin(), 
                   [](unsigned char c) { return std::tolower(c); });
    
    if (lowerTypeName == "kmbox") {
        return Create(MouseControllerType::KMBox);
    }
    else if (lowerTypeName == "system") {
        return Create(MouseControllerType::System);
    }
    else if (lowerTypeName == "logitech") {
        return Create(MouseControllerType::Logitech);
    }
    else if (lowerTypeName == "razer") {
        return Create(MouseControllerType::Razer);
    }
    else if (lowerTypeName == "mock") {
        return Create(MouseControllerType::Mock);
    }
    else {
        std::cout << "未知的控制器类型名称: " << typeName << std::endl;
        return nullptr;
    }
}

std::vector<std::string> MouseControllerFactory::GetSupportedTypes() {
    return {
        "kmbox",      // KMBoxNet硬件控制（已实现）
        "system",     // Windows系统API控制（待实现）
        "logitech",   // 罗技鼠标驱动控制（待实现）
        "razer",      // 雷蛇鼠标驱动控制（待实现）
        "mock"        // 模拟控制器（待实现，用于测试）
    };
}
