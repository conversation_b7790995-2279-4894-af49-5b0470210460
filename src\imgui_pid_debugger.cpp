#include "imgui_pid_debugger.h"
#include <iostream>

PIDDebugger::PIDDebugger() {
    m_configCache = PIDController::PIDConfig();
}

PIDDebugger::~PIDDebugger() {
    Shutdown();
}

bool PIDDebugger::Initialize(ID3D11Device* device, ID3D11DeviceContext* deviceContext, HWND hwnd) {
    m_device = device;
    m_deviceContext = deviceContext;
    m_hwnd = hwnd;
    m_initialized = true;
    std::cout << "PID调试器初始化成功（简化版本）" << std::endl;
    return true;
}

void PIDDebugger::SetPIDController(PIDController* pidController) {
    m_pidController = pidController;
    if (m_pidController) {
        m_configCache = m_pidController->GetConfig();
    }
}

void PIDDebugger::SetExternalParams(float* yOffsetRatioHead, float* yOffsetRatioBody) {
    m_yOffsetRatioHead = yOffsetRatioHead;
    m_yOffsetRatioBody = yOffsetRatioBody;
}

void PIDDebugger::NewFrame() {}

bool PIDDebugger::RenderDebugWindow(bool& isVisible) {
    if (isVisible && m_pidController) {
        static int frameCount = 0;
        frameCount++;

        if (frameCount % 60 == 0) {
            auto config = m_pidController->GetConfig();
            auto debugInfo = m_pidController->GetDebugInfo();

            std::cout << "\n=== PID调试信息 ===" << std::endl;
            std::cout << "Kp: " << config.kp << ", Ki: " << config.ki << ", Kd: " << config.kd << std::endl;
            std::cout << "上次误差: " << debugInfo.lastError.Length() << std::endl;
            std::cout << "总输出: " << debugInfo.totalOutput.Length() << std::endl;
            std::cout << "积分项: " << debugInfo.integral.Length() << std::endl;
            std::cout << "==================" << std::endl;
        }
    }
    return false;
}

void PIDDebugger::EndFrame() {}
bool PIDDebugger::ProcessMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) { return false; }
void PIDDebugger::Shutdown() { if (m_initialized) { m_initialized = false; std::cout << "PID调试器已关闭" << std::endl; } }
void PIDDebugger::RenderBasicParams() {}
void PIDDebugger::RenderAdvancedParams() {}
void PIDDebugger::RenderAdaptiveParams() {}
void PIDDebugger::RenderDebugInfo() {}
void PIDDebugger::RenderPresets() {}
void PIDDebugger::ApplyConfigChanges() {}
void PIDDebugger::SetupImGuiStyle() {}