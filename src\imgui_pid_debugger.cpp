#include "imgui_pid_debugger.h"
#include <iostream>
#include <ctime>

// 外部声明ImGui Win32处理函数
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

PIDDebugger::PIDDebugger() {
    m_configCache = PIDController::PIDConfig();
}

PIDDebugger::~PIDDebugger() {
    Shutdown();
}

bool PIDDebugger::Initialize(ID3D11Device* device, ID3D11DeviceContext* deviceContext, HWND hwnd) {
    m_device = device;
    m_deviceContext = deviceContext;
    m_hwnd = hwnd;

    // 不再创建新的ImGui上下文，而是使用现有的
    // 只设置我们的样式
    SetupImGuiStyle();

    m_initialized = true;
    std::cout << "PID调试器初始化成功（使用现有ImGui上下文）" << std::endl;
    return true;
}

void PIDDebugger::SetPIDController(PIDController* pidController) {
    m_pidController = pidController;
    if (m_pidController) {
        m_configCache = m_pidController->GetConfig();
    }
}

void PIDDebugger::SetExternalParams(float* yOffsetRatioHead, float* yOffsetRatioBody) {
    m_yOffsetRatioHead = yOffsetRatioHead;
    m_yOffsetRatioBody = yOffsetRatioBody;
}

void PIDDebugger::NewFrame() {}

bool PIDDebugger::RenderDebugWindow(bool& isVisible) {
    if (!m_initialized || !m_pidController || !isVisible) return false;

    // 获取当前配置
    m_configCache = m_pidController->GetConfig();
    m_configChanged = false;

    // 获取屏幕尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // 设置窗口位置和大小（参考你的代码风格）
    static bool winPos = true;
    if (winPos) {
        ImGui::SetNextWindowPos({ float(screenWidth - 700) / 2, float(screenHeight - 500) / 2 });
        winPos = false;
    }

    // 推送中文字体（如果可用）
    ImGuiIO& io = ImGui::GetIO();
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr) {
        ImGui::PushFont(io.Fonts->Fonts[0]);
    }

    // 开始窗口（使用你的窗口标志）
    ImGui::Begin("PID参数调试器", &isVisible, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoSavedSettings);
    ImGui::SetWindowSize({ 700.0f, 500.0f });

    // 获取样式和颜色（参考你的代码）
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 绘制分割线（参考你的设计）
    ImGui::GetWindowDrawList()->AddLine(
        { ImGui::GetWindowPos().x + 520.0f, ImGui::GetWindowPos().y + 10.0f },
        { ImGui::GetWindowPos().x + 520.0f, ImGui::GetWindowPos().y + 490.0f },
        ImColor(100, 100, 100, 255)
    );

    // 右侧标题和Tab按钮
    RenderSidePanel();

    // 左侧内容区域
    ImGui::SetCursorPos({ 10.0f, 10.0f });
    ImGui::BeginChild(u8"PIDContent", { 500.0f, 480.0f }, true);

    // 根据当前Tab渲染不同内容
    switch (m_currentTab) {
        case TabType::BasicPID:
            RenderBasicParams();
            break;
        case TabType::Advanced:
            RenderAdvancedParams();
            break;
        case TabType::Adaptive:
            RenderAdaptiveParams();
            break;
        case TabType::Debug:
            RenderDebugInfo();
            break;
    }

    ImGui::EndChild();
    ImGui::End();

    // 弹出字体
    ImGuiIO& io = ImGui::GetIO();
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr) {
        ImGui::PopFont();
    }

    // 如果有参数变化，应用到PID控制器
    if (m_configChanged) {
        ApplyConfigChanges();
    }

    return m_configChanged;
}

void PIDDebugger::EndFrame() {}
bool PIDDebugger::ProcessMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) { return false; }
void PIDDebugger::Shutdown() {
    if (m_initialized) {
        // 不关闭ImGui上下文，因为主程序还在使用
        m_initialized = false;
        std::cout << "PID调试器已关闭" << std::endl;
    }
}
void PIDDebugger::RenderBasicParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(u8"基本PID参数");
    ImGui::Separator();
    ImGui::Spacing();

    // KP参数
    ImGui::Text(u8"比例系数 (Kp):");
    ImGui::SameLine();
    HelpMarker(u8"控制当前误差的响应强度\n越大响应越快但易超调", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##kp", &m_configCache.kp, 0.0f, 5.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // KI参数
    ImGui::Text(u8"积分系数 (Ki):");
    ImGui::SameLine();
    HelpMarker(u8"消除稳态误差\n累积历史误差进行补偿", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##ki", &m_configCache.ki, 0.0f, 1.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // KD参数
    ImGui::Text(u8"微分系数 (Kd):");
    ImGui::SameLine();
    HelpMarker(u8"预测误差变化趋势\n减少超调和震荡", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##kd", &m_configCache.kd, 0.0f, 1.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Spacing();

    // 输出限制
    ImGui::Text(u8"输出限制");
    ImGui::Spacing();

    ImGui::Text(u8"最大输出:");
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##maxOutput", &m_configCache.maxOutput, 1.0f, 500.0f, "%.1f px")) {
        m_configChanged = true;
    }

    ImGui::Text(u8"死区范围:");
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##deadZone", &m_configCache.deadZone, 0.0f, 10.0f, "%.1f px")) {
        m_configChanged = true;
    }

    // Y轴偏移（外部参数）
    if (m_yOffsetRatioHead && m_yOffsetRatioBody) {
        ImGui::Spacing();
        ImGui::Text(u8"Y轴偏移比例:");

        ImGui::Text(u8"头部偏移:");
        ImGui::SetNextItemWidth(300.0f);
        ImGui::SliderFloat(u8"##yOffsetHead", m_yOffsetRatioHead, -1.0f, 1.0f, "%.2f");

        ImGui::Text(u8"身体偏移:");
        ImGui::SetNextItemWidth(300.0f);
        ImGui::SliderFloat(u8"##yOffsetBody", m_yOffsetRatioBody, -1.0f, 1.0f, "%.2f");
    }
}
void PIDDebugger::RenderAdvancedParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(u8"高级参数");
    ImGui::Separator();
    ImGui::Spacing();

    // 积分限幅
    ImGui::Text(u8"积分限幅:");
    ImGui::SameLine();
    HelpMarker(u8"防止积分项无限累积导致系统不稳定", color[ImGuiCol_Button]);
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat(u8"##maxIntegral", &m_configCache.maxIntegral, 1.0f, 200.0f, "%.1f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // 积分衰减
    if (ImGui::Checkbox(u8"启用积分衰减", &m_configCache.enableIntegralDecay)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"每次计算后积分项自动衰减，防止饱和", color[ImGuiCol_Button]);

    if (m_configCache.enableIntegralDecay) {
        ImGui::Text(u8"积分衰减率:");
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat(u8"##integralDecayRate", &m_configCache.integralDecayRate, 0.8f, 1.0f, "%.3f")) {
            m_configChanged = true;
        }
    }

    ImGui::Spacing();

    // 微分滤波
    if (ImGui::Checkbox(u8"启用微分滤波", &m_configCache.enableDerivativeFilter)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"对微分项进行低通滤波，减少噪声", color[ImGuiCol_Button]);

    if (m_configCache.enableDerivativeFilter) {
        ImGui::Text(u8"微分滤波系数:");
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat(u8"##derivativeFilterAlpha", &m_configCache.derivativeFilterAlpha, 0.1f, 1.0f, "%.2f")) {
            m_configChanged = true;
        }
    }
}

void PIDDebugger::RenderAdaptiveParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(u8"自适应参数");
    ImGui::Separator();
    ImGui::Spacing();

    if (ImGui::Checkbox(u8"启用自适应增益", &m_configCache.enableAdaptiveGain)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"根据误差大小动态调整增益", color[ImGuiCol_Button]);

    if (m_configCache.enableAdaptiveGain) {
        ImGui::Spacing();

        ImGui::Text(u8"增益因子:");
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat(u8"##adaptiveGainFactor", &m_configCache.adaptiveGainFactor, 1.0f, 5.0f, "%.2f")) {
            m_configChanged = true;
        }

        ImGui::Text(u8"自适应阈值:");
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat(u8"##adaptiveThreshold", &m_configCache.adaptiveThreshold, 5.0f, 100.0f, "%.1f px")) {
            m_configChanged = true;
        }
    } else {
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), u8"启用自适应增益以显示更多选项");
    }
}

void PIDDebugger::RenderDebugInfo() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(u8"实时调试信息");
    ImGui::Separator();
    ImGui::Spacing();

    if (m_pidController) {
        auto debugInfo = m_pidController->GetDebugInfo();

        // 当前状态
        ImGui::Text(u8"当前状态:");
        ImGui::Text(u8"上次误差: %.2f px", debugInfo.lastError.Length());
        ImGui::Text(u8"积分项: %.2f", debugInfo.integral.Length());
        ImGui::Text(u8"微分项: %.2f", debugInfo.derivative.Length());
        ImGui::Text(u8"总输出: %.2f px", debugInfo.totalOutput.Length());
        ImGui::Text(u8"时间间隔: %.3f ms", debugInfo.deltaTime * 1000.0f);
        ImGui::Text(u8"死区状态: %s", debugInfo.inDeadZone ? u8"是" : u8"否");

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // PID分量
        ImGui::Text(u8"PID分量:");
        ImGui::Text(u8"P输出: %.2f px", debugInfo.pOutput.Length());
        ImGui::Text(u8"I输出: %.2f px", debugInfo.iOutput.Length());
        ImGui::Text(u8"D输出: %.2f px", debugInfo.dOutput.Length());

        // 简单的历史图表
        static float errorHistory[100] = {};
        static int historyIndex = 0;

        errorHistory[historyIndex] = debugInfo.lastError.Length();
        historyIndex = (historyIndex + 1) % 100;

        ImGui::Spacing();
        ImGui::Text(u8"误差历史:");
        ImGui::PlotLines(u8"##ErrorHistory", errorHistory, 100, historyIndex, nullptr, 0.0f, 100.0f, ImVec2(450, 80));
    } else {
        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), u8"PID控制器未连接");
    }
}

void PIDDebugger::RenderPresets() {
    // 这个方法现在在RenderSidePanel中实现
}

void PIDDebugger::ApplyConfigChanges() {
    if (m_pidController && m_configChanged) {
        m_pidController->UpdateConfig(m_configCache);
        m_configChanged = false;
    }
}
void PIDDebugger::SetupImGuiStyle() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 设置圆角（参考你的代码）
    style.ChildRounding = 8.0f;
    style.FrameRounding = 5.0f;
    style.WindowRounding = 8.0f;

    // 蓝色主题（参考你的代码）
    color[ImGuiCol_Button] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_ButtonHovered] = ImColor(71, 140, 255, 255);
    color[ImGuiCol_ButtonActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_FrameBg] = ImColor(54, 54, 54, 150);
    color[ImGuiCol_FrameBgActive] = ImColor(42, 42, 42, 150);
    color[ImGuiCol_FrameBgHovered] = ImColor(100, 100, 100, 150);

    color[ImGuiCol_CheckMark] = ImColor(51, 120, 255, 255);

    color[ImGuiCol_SliderGrab] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_SliderGrabActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_Header] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_HeaderHovered] = ImColor(71, 140, 255, 255);
    color[ImGuiCol_HeaderActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_WindowBg] = ImColor(15, 15, 15, 240);
    color[ImGuiCol_ChildBg] = ImColor(0, 0, 0, 0);
}

void PIDDebugger::HelpMarker(const char* text, ImVec4 color) {
    ImGui::TextColored(color, u8"(?)");
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip(text);
    }
}

void PIDDebugger::RenderSidePanel() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 标题（参考你的代码风格）
    ImGui::SetCursorPos({ 530.0f, 20.0f });

    // 使用大字体显示标题
    ImGuiIO& io = ImGui::GetIO();
    if (io.Fonts->Fonts.Size > 1) {
        ImGui::PushFont(io.Fonts->Fonts[1]); // 使用大字体
        ImGui::TextColored(color[ImGuiCol_Button], "PID调试器");
        ImGui::PopFont();
    } else {
        ImGui::TextColored(color[ImGuiCol_Button], "PID调试器");
    }

    // Tab按钮（参考你的Tab设计）
    ImGui::SetCursorPos({ 530.0f, 65.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::BasicPID ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button("基本PID", { 150.0f, 40.0f })) {
        m_currentTab = TabType::BasicPID;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 115.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Advanced ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button("高级参数", { 150.0f, 40.0f })) {
        m_currentTab = TabType::Advanced;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 165.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Adaptive ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button("自适应", { 150.0f, 40.0f })) {
        m_currentTab = TabType::Adaptive;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 215.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Debug ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button("调试信息", { 150.0f, 40.0f })) {
        m_currentTab = TabType::Debug;
    }
    ImGui::PopStyleColor();

    // 预设按钮
    ImGui::SetCursorPos({ 530.0f, 275.0f });
    ImGui::Text("快速预设:");

    ImGui::SetCursorPos({ 530.0f, 300.0f });
    if (ImGui::Button("快速", { 70.0f, 30.0f })) {
        m_configCache.kp = 1.2f;
        m_configCache.ki = 0.0f;
        m_configCache.kd = 0.3f;
        m_configChanged = true;
    }
    ImGui::SameLine();
    if (ImGui::Button("平衡", { 70.0f, 30.0f })) {
        m_configCache.kp = 0.8f;
        m_configCache.ki = 0.1f;
        m_configCache.kd = 0.2f;
        m_configChanged = true;
    }

    ImGui::SetCursorPos({ 530.0f, 340.0f });
    if (ImGui::Button("精确", { 70.0f, 30.0f })) {
        m_configCache.kp = 0.6f;
        m_configCache.ki = 0.05f;
        m_configCache.kd = 0.4f;
        m_configChanged = true;
    }
    ImGui::SameLine();
    if (ImGui::Button("重置", { 70.0f, 30.0f })) {
        m_configCache = PIDController::PIDConfig();
        m_configChanged = true;
    }

    // 时间显示（参考你的代码）
    time_t t = time(0);
    char tmp[32] = { NULL };
    strftime(tmp, sizeof(tmp), "%H:%M:%S", localtime(&t));

    ImGui::SetCursorPos({ 530.0f, 450.0f });
    ImGui::TextColored(color[ImGuiCol_Button], "%s", tmp);
}