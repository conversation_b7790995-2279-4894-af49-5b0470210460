#include "imgui_pid_debugger.h"
#include <iostream>
#include <ctime>

// 中文字符串常量（使用UTF-8编码）
namespace ChineseText {
    const char* TITLE = u8"PID调试器";
    const char* BASIC_PID = u8"基本PID";
    const char* ADVANCED = u8"高级参数";
    const char* ADAPTIVE = u8"自适应";
    const char* DEBUG_INFO = u8"调试信息";


    // 基本参数
    const char* BASIC_PARAMS = u8"基本PID参数";
    const char* PROP_COEFF = u8"比例系数 (Kp):";
    const char* INTEG_COEFF = u8"积分系数 (Ki):";
    const char* DERIV_COEFF = u8"微分系数 (Kd):";
    const char* OUTPUT_LIMIT = u8"输出限制";
    const char* MAX_OUTPUT = u8"最大输出:";
    const char* DEAD_ZONE = u8"死区范围:";
    const char* Y_OFFSET_RATIO = u8"Y轴偏移比例:";
    const char* HEAD_OFFSET = u8"头部偏移:";
    const char* BODY_OFFSET = u8"身体偏移:";

    // 高级参数
    const char* ADVANCED_PARAMS = u8"高级参数";
    const char* INTEGRAL_LIMIT = u8"积分限幅:";
    const char* ENABLE_INTEGRAL_DECAY = u8"启用积分衰减";
    const char* INTEGRAL_DECAY_RATE = u8"积分衰减率:";
    const char* ENABLE_DERIVATIVE_FILTER = u8"启用微分滤波";
    const char* DERIVATIVE_FILTER_COEFF = u8"微分滤波系数:";

    // 自适应参数
    const char* ADAPTIVE_PARAMS = u8"自适应参数";
    const char* ENABLE_ADAPTIVE_GAIN = u8"启用自适应增益";
    const char* GAIN_FACTOR = u8"增益因子:";
    const char* ADAPTIVE_THRESHOLD = u8"自适应阈值:";
    const char* ENABLE_ADAPTIVE_HINT = u8"启用自适应增益以显示更多选项";

    // 调试信息
    const char* REALTIME_DEBUG = u8"实时调试信息";
    const char* CURRENT_STATE = u8"当前状态:";
    const char* LAST_ERROR = u8"上次误差: %.2f px";
    const char* INTEGRAL_TERM = u8"积分项: %.2f";
    const char* DERIVATIVE_TERM = u8"微分项: %.2f";
    const char* TOTAL_OUTPUT = u8"总输出: %.2f px";
    const char* TIME_INTERVAL = u8"时间间隔: %.3f ms";
    const char* DEAD_ZONE_STATUS = u8"死区状态: %s";
    const char* YES = u8"是";
    const char* NO = u8"否";
    const char* PID_COMPONENTS = u8"PID分量:";
    const char* P_OUTPUT = u8"P输出: %.2f px";
    const char* I_OUTPUT = u8"I输出: %.2f px";
    const char* D_OUTPUT = u8"D输出: %.2f px";
    const char* ERROR_HISTORY = u8"误差历史:";
    const char* PID_NOT_CONNECTED = u8"PID控制器未连接";

    // 运动预测参数
    const char* MOTION_PREDICTION = u8"运动预测";
    const char* ENABLE_MOTION_PREDICTION = u8"启用运动预测";
    const char* PREDICTION_TIME = u8"预测时间:";
    const char* VELOCITY_SMOOTHING = u8"速度平滑:";
    const char* MIN_PREDICTION_SPEED = u8"最小预测速度:";
    const char* MOTION_PREDICTION_HINT = u8"启用运动预测以显示更多选项";
    const char* TARGET_VELOCITY = u8"目标速度: %.1f px/s";
    const char* SMOOTHED_VELOCITY = u8"平滑速度: %.1f px/s";
}

// 外部声明ImGui Win32处理函数
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

PIDDebugger::PIDDebugger() {
    m_configCache = PIDController::PIDConfig();
}

PIDDebugger::~PIDDebugger() {
    Shutdown();
}

bool PIDDebugger::Initialize(ID3D11Device* device, ID3D11DeviceContext* deviceContext, HWND hwnd) {
    m_device = device;
    m_deviceContext = deviceContext;
    m_hwnd = hwnd;

    // 不再创建新的ImGui上下文，而是使用现有的
    // 只设置我们的样式
    SetupImGuiStyle();

    m_initialized = true;
    std::cout << "PID调试器初始化成功（使用现有ImGui上下文）" << std::endl;
    return true;
}

void PIDDebugger::SetPIDController(PIDController* pidController) {
    m_pidController = pidController;
    if (m_pidController) {
        m_configCache = m_pidController->GetConfig();
    }
}

void PIDDebugger::SetExternalParams(float* yOffsetRatioHead, float* yOffsetRatioBody) {
    m_yOffsetRatioHead = yOffsetRatioHead;
    m_yOffsetRatioBody = yOffsetRatioBody;
}

void PIDDebugger::NewFrame() {}

bool PIDDebugger::RenderDebugWindow(bool& isVisible) {
    if (!m_initialized || !m_pidController || !isVisible) return false;

    // 获取当前配置
    m_configCache = m_pidController->GetConfig();
    m_configChanged = false;

    // 获取屏幕尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // 设置窗口位置和大小（参考你的代码风格）
    static bool winPos = true;
    if (winPos) {
        ImGui::SetNextWindowPos({ float(screenWidth - 700) / 2, float(screenHeight - 500) / 2 });
        winPos = false;
    }

    // 推送中文字体（如果可用）
    ImGuiIO& io = ImGui::GetIO();
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr) {
        ImGui::PushFont(io.Fonts->Fonts[0]);
    }

    // 开始窗口（使用你的窗口标志）
    ImGui::Begin("PID参数调试器", &isVisible, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoSavedSettings);
    ImGui::SetWindowSize({ 700.0f, 500.0f });

    // 获取样式和颜色（参考你的代码）
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 绘制分割线（参考你的设计）
    ImGui::GetWindowDrawList()->AddLine(
        { ImGui::GetWindowPos().x + 520.0f, ImGui::GetWindowPos().y + 10.0f },
        { ImGui::GetWindowPos().x + 520.0f, ImGui::GetWindowPos().y + 490.0f },
        ImColor(100, 100, 100, 255)
    );

    // 右侧标题和Tab按钮
    RenderSidePanel();

    // 左侧内容区域
    ImGui::SetCursorPos({ 10.0f, 10.0f });
    ImGui::BeginChild("PIDContent", { 500.0f, 480.0f }, true);

    // 根据当前Tab渲染不同内容
    switch (m_currentTab) {
        case TabType::BasicPID:
            RenderBasicParams();
            break;
        case TabType::Advanced:
            RenderAdvancedParams();
            break;
        case TabType::Adaptive:
            RenderAdaptiveParams();
            break;
        case TabType::Debug:
            RenderDebugInfo();
            break;
    }

    ImGui::EndChild();
    ImGui::End();

    // 弹出字体
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr) {
        ImGui::PopFont();
    }

    // 如果有参数变化，应用到PID控制器
    if (m_configChanged) {
        ApplyConfigChanges();
    }

    return m_configChanged;
}

void PIDDebugger::EndFrame() {}
bool PIDDebugger::ProcessMessage(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) { return false; }
void PIDDebugger::Shutdown() {
    if (m_initialized) {
        // 不关闭ImGui上下文，因为主程序还在使用
        m_initialized = false;
        std::cout << "PID调试器已关闭" << std::endl;
    }
}
void PIDDebugger::RenderBasicParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(ChineseText::BASIC_PARAMS);
    ImGui::Separator();
    ImGui::Spacing();

    // KP参数
    ImGui::Text(ChineseText::PROP_COEFF);
    ImGui::SameLine();
    HelpMarker(u8"控制当前误差的响应强度\n越大响应越快但易超调", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##kp", &m_configCache.kp, 0.0f, 5.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // KI参数
    ImGui::Text(ChineseText::INTEG_COEFF);
    ImGui::SameLine();
    HelpMarker(u8"消除稳态误差\n累积历史误差进行补偿", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##ki", &m_configCache.ki, 0.0f, 1.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // KD参数
    ImGui::Text(ChineseText::DERIV_COEFF);
    ImGui::SameLine();
    HelpMarker(u8"预测误差变化趋势\n减少超调和震荡", color[ImGuiCol_Button]);

    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##kd", &m_configCache.kd, 0.0f, 1.0f, "%.3f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Spacing();

    // 输出限制
    ImGui::Text(ChineseText::OUTPUT_LIMIT);
    ImGui::Spacing();

    ImGui::Text(ChineseText::MAX_OUTPUT);
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##maxOutput", &m_configCache.maxOutput, 1.0f, 500.0f, "%.1f px")) {
        m_configChanged = true;
    }

    ImGui::Text(ChineseText::DEAD_ZONE);
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##deadZone", &m_configCache.deadZone, 0.0f, 10.0f, "%.1f px")) {
        m_configChanged = true;
    }

    // Y轴偏移（外部参数）
    if (m_yOffsetRatioHead && m_yOffsetRatioBody) {
        ImGui::Spacing();
        ImGui::Text(ChineseText::Y_OFFSET_RATIO);

        ImGui::Text(ChineseText::HEAD_OFFSET);
        ImGui::SetNextItemWidth(300.0f);
        ImGui::SliderFloat("##yOffsetHead", m_yOffsetRatioHead, -1.0f, 1.0f, "%.2f");

        ImGui::Text(ChineseText::BODY_OFFSET);
        ImGui::SetNextItemWidth(300.0f);
        ImGui::SliderFloat("##yOffsetBody", m_yOffsetRatioBody, -1.0f, 1.0f, "%.2f");
    }
}
void PIDDebugger::RenderAdvancedParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(ChineseText::ADVANCED_PARAMS);
    ImGui::Separator();
    ImGui::Spacing();

    // 积分限幅
    ImGui::Text(ChineseText::INTEGRAL_LIMIT);
    ImGui::SameLine();
    HelpMarker(u8"防止积分项无限累积导致系统不稳定", color[ImGuiCol_Button]);
    ImGui::SetNextItemWidth(300.0f);
    if (ImGui::SliderFloat("##maxIntegral", &m_configCache.maxIntegral, 1.0f, 200.0f, "%.1f")) {
        m_configChanged = true;
    }

    ImGui::Spacing();

    // 积分衰减
    if (ImGui::Checkbox(ChineseText::ENABLE_INTEGRAL_DECAY, &m_configCache.enableIntegralDecay)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"每次计算后积分项自动衰减，防止饱和", color[ImGuiCol_Button]);

    if (m_configCache.enableIntegralDecay) {
        ImGui::Text(ChineseText::INTEGRAL_DECAY_RATE);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##integralDecayRate", &m_configCache.integralDecayRate, 0.8f, 1.0f, "%.3f")) {
            m_configChanged = true;
        }
    }

    ImGui::Spacing();

    // 微分滤波
    if (ImGui::Checkbox(ChineseText::ENABLE_DERIVATIVE_FILTER, &m_configCache.enableDerivativeFilter)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"对微分项进行低通滤波，减少噪声", color[ImGuiCol_Button]);

    if (m_configCache.enableDerivativeFilter) {
        ImGui::Text(ChineseText::DERIVATIVE_FILTER_COEFF);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##derivativeFilterAlpha", &m_configCache.derivativeFilterAlpha, 0.1f, 1.0f, "%.2f")) {
            m_configChanged = true;
        }
    }

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Spacing();

    // 运动预测参数
    ImGui::Text(ChineseText::MOTION_PREDICTION);
    ImGui::Separator();
    ImGui::Spacing();

    if (ImGui::Checkbox(ChineseText::ENABLE_MOTION_PREDICTION, &m_configCache.enableMotionPrediction)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"启用目标运动预测，显著改善对移动目标的跟踪效果", color[ImGuiCol_Button]);

    if (m_configCache.enableMotionPrediction) {
        ImGui::Spacing();

        ImGui::Text(ChineseText::PREDICTION_TIME);
        ImGui::SameLine();
        HelpMarker(u8"预测未来多少秒的目标位置，建议0.05-0.2秒", color[ImGuiCol_Button]);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##predictionTime", &m_configCache.predictionTime, 0.01f, 0.5f, "%.3f s")) {
            m_configChanged = true;
        }

        ImGui::Text(ChineseText::VELOCITY_SMOOTHING);
        ImGui::SameLine();
        HelpMarker(u8"速度平滑系数，越大越平滑但响应越慢", color[ImGuiCol_Button]);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##velocitySmoothing", &m_configCache.velocitySmoothing, 0.1f, 0.95f, "%.2f")) {
            m_configChanged = true;
        }

        ImGui::Text(ChineseText::MIN_PREDICTION_SPEED);
        ImGui::SameLine();
        HelpMarker(u8"只有当目标速度超过此值时才进行预测", color[ImGuiCol_Button]);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##minPredictionSpeed", &m_configCache.minPredictionSpeed, 1.0f, 50.0f, "%.1f px/s")) {
            m_configChanged = true;
        }
    } else {
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), ChineseText::MOTION_PREDICTION_HINT);
    }
}

void PIDDebugger::RenderAdaptiveParams() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(ChineseText::ADAPTIVE_PARAMS);
    ImGui::Separator();
    ImGui::Spacing();

    if (ImGui::Checkbox(ChineseText::ENABLE_ADAPTIVE_GAIN, &m_configCache.enableAdaptiveGain)) {
        m_configChanged = true;
    }
    ImGui::SameLine();
    HelpMarker(u8"根据误差大小动态调整增益", color[ImGuiCol_Button]);

    if (m_configCache.enableAdaptiveGain) {
        ImGui::Spacing();

        ImGui::Text(ChineseText::GAIN_FACTOR);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##adaptiveGainFactor", &m_configCache.adaptiveGainFactor, 1.0f, 5.0f, "%.2f")) {
            m_configChanged = true;
        }

        ImGui::Text(ChineseText::ADAPTIVE_THRESHOLD);
        ImGui::SetNextItemWidth(300.0f);
        if (ImGui::SliderFloat("##adaptiveThreshold", &m_configCache.adaptiveThreshold, 5.0f, 100.0f, "%.1f px")) {
            m_configChanged = true;
        }
    } else {
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), ChineseText::ENABLE_ADAPTIVE_HINT);
    }
}

void PIDDebugger::RenderDebugInfo() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    ImGui::Text(ChineseText::REALTIME_DEBUG);
    ImGui::Separator();
    ImGui::Spacing();

    if (m_pidController) {
        auto debugInfo = m_pidController->GetDebugInfo();

        // 当前状态
        ImGui::Text(ChineseText::CURRENT_STATE);
        ImGui::Text(ChineseText::LAST_ERROR, debugInfo.lastError.Length());
        ImGui::Text(ChineseText::INTEGRAL_TERM, debugInfo.integral.Length());
        ImGui::Text(ChineseText::DERIVATIVE_TERM, debugInfo.derivative.Length());
        ImGui::Text(ChineseText::TOTAL_OUTPUT, debugInfo.totalOutput.Length());
        ImGui::Text(ChineseText::TIME_INTERVAL, debugInfo.deltaTime * 1000.0f);
        ImGui::Text(ChineseText::DEAD_ZONE_STATUS, debugInfo.inDeadZone ? ChineseText::YES : ChineseText::NO);

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // PID分量
        ImGui::Text(ChineseText::PID_COMPONENTS);
        ImGui::Text(ChineseText::P_OUTPUT, debugInfo.pOutput.Length());
        ImGui::Text(ChineseText::I_OUTPUT, debugInfo.iOutput.Length());
        ImGui::Text(ChineseText::D_OUTPUT, debugInfo.dOutput.Length());

        // 简单的历史图表
        static float errorHistory[100] = {};
        static int historyIndex = 0;

        errorHistory[historyIndex] = debugInfo.lastError.Length();
        historyIndex = (historyIndex + 1) % 100;

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // 运动预测信息
        if (m_pidController->GetConfig().enableMotionPrediction) {
            ImGui::Text(u8"运动预测状态:");
            // 注意：这里需要扩展DebugInfo结构来包含速度信息
            // 暂时显示预测功能已启用
            ImGui::Text(u8"预测功能: 已启用");
            ImGui::Text(u8"预测时间: %.3f 秒", m_pidController->GetConfig().predictionTime);
        }

        ImGui::Spacing();
        ImGui::Text(ChineseText::ERROR_HISTORY);
        ImGui::PlotLines("##ErrorHistory", errorHistory, 100, historyIndex, nullptr, 0.0f, 100.0f, ImVec2(450, 80));
    } else {
        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), ChineseText::PID_NOT_CONNECTED);
    }
}



void PIDDebugger::ApplyConfigChanges() {
    if (m_pidController && m_configChanged) {
        m_pidController->UpdateConfig(m_configCache);
        m_configChanged = false;
    }
}
void PIDDebugger::SetupImGuiStyle() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 设置圆角（参考你的代码）
    style.ChildRounding = 8.0f;
    style.FrameRounding = 5.0f;
    style.WindowRounding = 8.0f;

    // 蓝色主题（参考你的代码）
    color[ImGuiCol_Button] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_ButtonHovered] = ImColor(71, 140, 255, 255);
    color[ImGuiCol_ButtonActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_FrameBg] = ImColor(54, 54, 54, 150);
    color[ImGuiCol_FrameBgActive] = ImColor(42, 42, 42, 150);
    color[ImGuiCol_FrameBgHovered] = ImColor(100, 100, 100, 150);

    color[ImGuiCol_CheckMark] = ImColor(51, 120, 255, 255);

    color[ImGuiCol_SliderGrab] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_SliderGrabActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_Header] = ImColor(51, 120, 255, 255);
    color[ImGuiCol_HeaderHovered] = ImColor(71, 140, 255, 255);
    color[ImGuiCol_HeaderActive] = ImColor(31, 100, 225, 255);

    color[ImGuiCol_WindowBg] = ImColor(15, 15, 15, 240);
    color[ImGuiCol_ChildBg] = ImColor(0, 0, 0, 0);
}

void PIDDebugger::HelpMarker(const char* text, ImVec4 color) {
    ImGui::TextColored(color, "(?)");
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip(text);
    }
}

void PIDDebugger::RenderSidePanel() {
    ImGuiStyle& style = ImGui::GetStyle();
    auto color = style.Colors;

    // 标题（参考你的代码风格）
    ImGui::SetCursorPos({ 530.0f, 20.0f });

    // 使用大字体显示标题
    ImGuiIO& io = ImGui::GetIO();
    if (io.Fonts->Fonts.Size > 1) {
        ImGui::PushFont(io.Fonts->Fonts[1]); // 使用大字体
        ImGui::TextColored(color[ImGuiCol_Button], ChineseText::TITLE);
        ImGui::PopFont();
    } else {
        ImGui::TextColored(color[ImGuiCol_Button], ChineseText::TITLE);
    }

    // Tab按钮（参考你的Tab设计）
    ImGui::SetCursorPos({ 530.0f, 65.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::BasicPID ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button(ChineseText::BASIC_PID, { 150.0f, 40.0f })) {
        m_currentTab = TabType::BasicPID;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 115.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Advanced ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button(ChineseText::ADVANCED, { 150.0f, 40.0f })) {
        m_currentTab = TabType::Advanced;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 165.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Adaptive ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button(ChineseText::ADAPTIVE, { 150.0f, 40.0f })) {
        m_currentTab = TabType::Adaptive;
    }
    ImGui::PopStyleColor();

    ImGui::SetCursorPos({ 530.0f, 215.0f });
    ImGui::PushStyleColor(ImGuiCol_Button, m_currentTab == TabType::Debug ? color[ImGuiCol_Button] : ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::Button(ChineseText::DEBUG_INFO, { 150.0f, 40.0f })) {
        m_currentTab = TabType::Debug;
    }
    ImGui::PopStyleColor();



    // 时间显示（参考你的代码）
    time_t t = time(0);
    char tmp[32] = { NULL };
    strftime(tmp, sizeof(tmp), "%H:%M:%S", localtime(&t));

    ImGui::SetCursorPos({ 530.0f, 450.0f });
    ImGui::TextColored(color[ImGuiCol_Button], "%s", tmp);
}