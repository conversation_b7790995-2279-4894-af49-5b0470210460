# -*- coding: utf-8 -*-
# 鏂囦欢鍚�: postprocess_enhanced.py
# 鍔熻兘: 澧炲己鐗堟帹鐞嗙粨鏋滃悗澶勭悊妯″潡锛岃兘鑷姩閫傞厤涓嶅悓YOLO妯″瀷鏍煎紡

# --- 瀵煎叆搴� ---
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any, Optional, Union
import os

# 瀵煎叆鎴戜滑鐨勬ā鍨嬫帰娴嬪櫒
try:
    from yolo_model_prober import YOLOModelProber
    PROBER_AVAILABLE = True
except ImportError:
    PROBER_AVAILABLE = False
    print("璀﹀憡: 鏈壘鍒癥OLOModelProber妯″潡锛屽皢浣跨敤閫氱敤澶勭悊閫昏緫銆�")

class EnhancedDetectionPostProcessor:
    """
    澧炲己鐗堢洰鏍囨娴嬬粨鏋滃悗澶勭悊绫伙紝鑳藉鑷姩閫傞厤涓嶅悓YOLO妯″瀷鏍煎紡
    """
    def __init__(self, 
                 confidence_threshold: float = 0.5, 
                 nms_threshold: float = 0.1, 
                 class_names: List[str] = None,
                 model_path: Optional[str] = None,
                 force_model_type: Optional[str] = None,
                 num_classes: int = 0):
        """
        鍒濆鍖栧悗澶勭悊鍣�
        
        Args:
            confidence_threshold: 缃俊搴﹂槇鍊硷紝浣庝簬姝ゅ€肩殑妫€娴嬪皢琚繃婊�
            nms_threshold: 闈炴瀬澶ф姂鍒堕噸鍙犻槇鍊�
            class_names: 绫诲埆鍚嶇О鍒楄〃
            model_path: YOLO妯″瀷鏂囦欢璺緞锛岀敤浜庤嚜鍔ㄦ帰娴嬫ā鍨嬫牸寮�
            force_model_type: 寮哄埗鎸囧畾妯″瀷绫诲瀷 ('yolov5' 鎴� 'yolov8')
            num_classes: 绫诲埆鏁伴噺锛屽鏋滀负0鍒欒嚜鍔ㄦ娴�
        """
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        
        # 榛樿绫诲埆鍚嶇О锛屽悗缁彲鑳戒細鏍规嵁妯″瀷鎺㈡祴缁撴灉璋冩暣
        self.class_names = class_names if class_names is not None else ["澶撮儴", "韬綋"]
        self.format_printed = False
        
        # 妯″瀷鏍煎紡璁剧疆
        self.model_settings = {
            "needs_transpose": None,  # 鏄惁闇€瑕佽浆缃�
            "box_format": "xywh",     # 杈圭晫妗嗘牸寮�
            "has_obj_confidence": True, # 鏄惁鏈塷bj缃俊搴�
            "class_offset": 5,        # 绫诲埆鍒嗘暟璧峰浣嶇疆
            "model_type": "unknown",  # 妯″瀷绫诲瀷
        }
        
        # 濡傛灉寮哄埗鎸囧畾浜嗘ā鍨嬬被鍨嬶紝鍒欎娇鐢ㄦ寚瀹氱殑璁剧疆
        if force_model_type:
            self._apply_forced_model_type(force_model_type, num_classes)
        # 濡傛灉鎻愪緵浜嗘ā鍨嬭矾寰勶紝灏濊瘯鎺㈡祴妯″瀷
        elif model_path and PROBER_AVAILABLE:
            self._probe_model(model_path)
        else:
            print("鏈彁渚涙ā鍨嬭矾寰勬垨妯″瀷鎺㈡祴鍣ㄤ笉鍙敤锛屽皢浣跨敤榛樿璁剧疆銆�")

    def _apply_forced_model_type(self, model_type: str, num_classes: int) -> None:
        """
        搴旂敤鐢ㄦ埛寮哄埗鎸囧畾鐨勬ā鍨嬬被鍨嬭缃�
        
        Args:
            model_type: 鎸囧畾鐨勬ā鍨嬬被鍨� ('yolov5' 鎴� 'yolov8')
            num_classes: 绫诲埆鏁伴噺
        """
        # 妫€鏌ユ槸鍚﹀凡缁忚缃繃鍚屾牱鐨勬ā鍨嬬被鍨嬶紝閬垮厤閲嶅杈撳嚭
        if hasattr(self, '_model_type_set') and self._model_type_set == model_type.lower():
            return
            
        # 鏍囪宸茬粡璁剧疆杩囨妯″瀷绫诲瀷
        self._model_type_set = model_type.lower()
        
        print(f"鐢ㄦ埛鎸囧畾妯″瀷绫诲瀷: {model_type.upper()}")
        
        if model_type.lower() == "yolov5":
            self.model_settings.update({
                "needs_transpose": None,  # 璁句负None鍏佽鑷姩鍒ゆ柇
                "has_obj_confidence": True,
                "class_offset": 5,
                "model_type": "yolov5"
            })
        elif model_type.lower() == "yolov8":
            self.model_settings.update({
                "needs_transpose": None,  # 璁句负None鍏佽鑷姩鍒ゆ柇
                "has_obj_confidence": False,
                "class_offset": 4,
                "model_type": "yolov8"
            })
        else:
            print(f"璀﹀憡: 鏈煡妯″瀷绫诲瀷 {model_type}锛屽皢浣跨敤榛樿璁剧疆銆�")
            return
            
        # 澶勭悊绫诲埆鏁伴噺
        if num_classes > 0 and len(self.class_names) != num_classes:
            print(f"璋冩暣绫诲埆鏁伴噺: {len(self.class_names)} -> {num_classes}")
            
            # 鐢熸垚閫氱敤绫诲埆鍚嶇О锛岀‘淇濇暟閲忎笌鎸囧畾鐨勭被鍒暟閲忓尮閰�
            if len(self.class_names) < num_classes:
                # 鐢熸垚鏂扮殑閫氱敤绫诲埆鍚嶇О
                for i in range(len(self.class_names), num_classes):
                    self.class_names.append(f"绫诲埆{i}")
            else:
                # 濡傛灉鎻愪緵鐨勭被鍒悕绉拌繃澶氾紝鎴柇鍒板尮閰嶆寚瀹氱被鍒暟
                self.class_names = self.class_names[:num_classes]
                
        # 鎵撳嵃璁剧疆淇℃伅
        print("\n=== 妯″瀷璁剧疆 (鐢ㄦ埛鎸囧畾) ===")
        print(f"妯″瀷绫诲瀷: {self.model_settings['model_type']}")
        print(f"闇€瑕佽浆缃�: {self.model_settings['needs_transpose']}")
        print(f"鏈夊璞＄疆淇″害: {self.model_settings['has_obj_confidence']}")
        print(f"绫诲埆璧峰浣嶇疆: {self.model_settings['class_offset']}")
        print(f"妫€娴嬫鏍煎紡: {self.model_settings['box_format']}")
        print(f"绫诲埆鏁伴噺: {len(self.class_names)}")
        print(f"绫诲埆鍚嶇О: {self.class_names}")
        print("==========================\n")

    def _probe_model(self, model_path: str) -> None:
        """
        鎺㈡祴妯″瀷鏍煎紡骞舵洿鏂拌缃�
        
        Args:
            model_path: 妯″瀷鏂囦欢璺緞
        """
        try:
            # 纭繚鏂囦欢瀛樺湪
            if not os.path.exists(model_path):
                print(f"閿欒: 妯″瀷鏂囦欢涓嶅瓨鍦� {model_path}")
                return
                
            print(f"姝ｅ湪鎺㈡祴妯″瀷: {model_path}")
            prober = YOLOModelProber(verbose=False)
            prober.probe(model_path)
            
            # 鑾峰彇鎺ㄨ崘璁剧疆
            settings = prober.get_recommended_processor_settings()
            model_info = prober.get_model_info()
            
            # 鏇存柊璁剧疆
            self.model_settings.update(settings)
            self.model_settings["model_type"] = model_info["model_type"]
            # 璁剧疆needs_transpose涓篘one锛岃澶勭悊鍑芥暟鏍规嵁瀹為檯杈撳叆鑷姩鍒ゆ柇
            self.model_settings["needs_transpose"] = None
            
            # 鑷姩閫傞厤绫诲埆鏁伴噺
            num_classes = model_info["format_info"]["num_classes"]
            if len(self.class_names) != num_classes:
                print(f"璀﹀憡: 绫诲埆鏁伴噺涓嶅尮閰� - 鎻愪緵浜唟len(self.class_names)}涓被鍒悕绉帮紝浣嗘ā鍨嬫湁{num_classes}涓被鍒�")
                print("姝ｅ湪鑷姩鐢熸垚閫氱敤绫诲埆鍚嶇О...")
                
                # 鐢熸垚閫氱敤绫诲埆鍚嶇О锛岀‘淇濇暟閲忎笌妯″瀷鍖归厤
                # 淇濈暀鍘熸湁鐨勭被鍒悕绉帮紝涓嶈冻鐨勯儴鍒嗚嚜鍔ㄧ敓鎴�
                if len(self.class_names) < num_classes:
                    # 鐢熸垚鏂扮殑閫氱敤绫诲埆鍚嶇О
                    for i in range(len(self.class_names), num_classes):
                        self.class_names.append(f"绫诲埆{i}")
                else:
                    # 濡傛灉鎻愪緵鐨勭被鍒悕绉拌繃澶氾紝鎴柇鍒板尮閰嶆ā鍨嬬被鍒暟
                    self.class_names = self.class_names[:num_classes]
                
                print(f"鑷姩鐢熸垚鐨勭被鍒悕绉�: {self.class_names}")
            
            # 鎵撳嵃璁剧疆淇℃伅
            print("\n=== 妯″瀷鏍煎紡鑷姩鎺㈡祴缁撴灉 ===")
            print(f"妯″瀷绫诲瀷: {self.model_settings['model_type']}")
            print(f"闇€瑕佽浆缃�: {self.model_settings['needs_transpose']}")
            print(f"鏈夊璞＄疆淇″害: {self.model_settings['has_obj_confidence']}")
            print(f"绫诲埆璧峰浣嶇疆: {self.model_settings['class_offset']}")
            print(f"妫€娴嬫鏍煎紡: {self.model_settings['box_format']}")
            print(f"绫诲埆鏁伴噺: {num_classes}")
            print(f"绫诲埆鍚嶇О: {self.class_names}")
            print("============================\n")
            
        except Exception as e:
            print(f"妯″瀷鎺㈡祴澶辫触: {str(e)}")
            print("灏嗕娇鐢ㄩ粯璁よ缃户缁€�")
    
    def process(self, output: any, original_img: np.ndarray, input_shape: Tuple[int, int],
                force_model_type: Optional[str] = None, num_classes: int = 0,
                class_names: List[str] = None) -> Tuple[np.ndarray, List[Dict]]:
        """
        缁熶竴澶勭悊鎵€鏈塝OLO鏍煎紡鐨勬ā鍨嬭緭鍑恒€�
        
        Args:
            output: 妯″瀷鐨勫師濮嬭緭鍑�
            original_img: 鍘熷杈撳叆鍥惧儚
            input_shape: 妯″瀷鐨勮緭鍏ュ昂瀵�
            force_model_type: 寮哄埗鎸囧畾妯″瀷绫诲瀷锛堣鐩栧垵濮嬪寲鏃剁殑璁剧疆锛�
            num_classes: 寮哄埗鎸囧畾绫诲埆鏁伴噺锛堣鐩栧垵濮嬪寲鏃剁殑璁剧疆锛�
            class_names: 寮哄埗鎸囧畾绫诲埆鍚嶇О锛堣鐩栧垵濮嬪寲鏃剁殑璁剧疆锛�
            
        Returns:
            (澶勭悊鍚庣殑鍥惧儚, 妫€娴嬬粨鏋滃垪琛�)
        """
        # 濡傛灉闇€瑕佷复鏃惰鐩栨ā鍨嬬被鍨嬭缃�
        temp_settings_applied = False
        original_settings = None
        original_class_names = None
        
        # 鍙湁褰撹缃彂鐢熷疄闄呭彉鍖栨椂鎵嶅簲鐢ㄤ复鏃惰缃�
        needs_model_type_update = (force_model_type and 
                                  (not hasattr(self, '_model_type_set') or 
                                   self._model_type_set != force_model_type.lower()))
        
        needs_class_names_update = (class_names and class_names != self.class_names)
        
        if needs_model_type_update or needs_class_names_update:
            # 淇濆瓨鍘熷璁剧疆
            temp_settings_applied = True
            original_settings = self.model_settings.copy()
            original_class_names = self.class_names.copy()
            
            # 搴旂敤涓存椂璁剧疆
            if needs_model_type_update:
                self._apply_forced_model_type(force_model_type, num_classes)
            
            # 搴旂敤绫诲埆鍚嶇О
            if needs_class_names_update:
                self.class_names = class_names
                
        # 纭繚绫诲埆鍚嶇О鍒楄〃涓嶄负绌猴紝濡傛灉涓虹┖鍒欏垱寤洪粯璁ょ被鍒悕绉�
        if not self.class_names:
            print("璀﹀憡: 绫诲埆鍚嶇О鍒楄〃涓虹┖锛屽垱寤洪粯璁ょ被鍒悕绉�")
            self.class_names = ["绫诲埆0", "绫诲埆1"]
        
        try:
            # --- 1. 鏁版嵁娓呮礂鍜屽褰� ---
            try:
                # a. 瑙ｅ寘锛氬簲瀵瑰祵濂楀厓缁勮緭鍑�
                tensor = output
                while isinstance(tensor, tuple):
                    tensor = tensor[0]
                
                # b. 鍒ゆ柇鏄惁闇€瑕佽浆缃�
                needs_transpose = self.model_settings["needs_transpose"]
                
                # 濡傛灉妯″瀷璁剧疆灏氭湭纭畾锛屽垯杩涜鑷姩鍒ゆ柇
                if needs_transpose is None:
                    # 妫€娴嬫鏁伴噺蹇呯劧杩滃ぇ浜庣壒寰佺淮搴�
                    # 濡傛灉绗簩缁村皬浜庣涓夌淮锛屽垯鏍煎紡涓� [1, 鐗瑰緛缁村害, 妫€娴嬫鏁伴噺]锛岄渶瑕佽浆缃�
                    # 濡傛灉绗簩缁村ぇ浜庣涓夌淮锛屽垯鏍煎紡涓� [1, 妫€娴嬫鏁伴噺, 鐗瑰緛缁村害]锛屼笉闇€瑕佽浆缃�
                    if len(tensor.shape) == 3:
                        # 姣旇緝绗簩缁村拰绗笁缁寸殑澶у皬
                        if tensor.shape[1] < tensor.shape[2]:
                            # 渚嬪 [1, 7, 6300] 鏍煎紡锛岄渶瑕佽浆缃�
                            needs_transpose = True
                            if not self.format_printed:
                                print(f"鑷姩妫€娴嬪埌杞疆鏍煎紡 {tensor.shape}, 绗簩缁�({tensor.shape[1]})灏忎簬绗笁缁�({tensor.shape[2]})锛屾帹鏂负 [batch, 鐗瑰緛缁村害, 妫€娴嬫鏁伴噺] 鏍煎紡锛屾鍦ㄨ浆缃�...")
                        else:
                            # 渚嬪 [1, 6300, 7] 鏍煎紡锛屼笉闇€瑕佽浆缃�
                            needs_transpose = False
                            if not self.format_printed:
                                print(f"妫€娴嬪埌鏍囧噯鏍煎紡 {tensor.shape}, 绗簩缁�({tensor.shape[1]})澶т簬绗笁缁�({tensor.shape[2]})锛屾帹鏂负 [batch, 妫€娴嬫鏁伴噺, 鐗瑰緛缁村害] 鏍煎紡")
                    else:
                        needs_transpose = False
                
                # 鎵ц杞疆
                if needs_transpose and len(tensor.shape) == 3:
                    if not self.format_printed:
                        print(f"杞疆鍓嶅舰鐘�: {tensor.shape}")
                    tensor = tensor.transpose(0, 2, 1)
                    if not self.format_printed:
                        print(f"杞疆鍚庡舰鐘�: {tensor.shape}")
                
                # c. 灞曞钩锛氬皢 (batch, boxes, dims) 缁熶竴涓� (N, dims) 鏍煎紡
                predictions = np.squeeze(tensor, axis=0)
                
                if not self.format_printed:
                    print(f"澶勭悊鐨勫紶閲忓舰鐘�: {predictions.shape}")
                    self.format_printed = True

            except Exception as e:
                if not self.format_printed:
                    print(f"鏁版嵁娓呮礂闃舵鍑洪敊: {e}, 杈撳叆绫诲瀷: {type(output)}")
                    self.format_printed = True
                return original_img, []

            # --- 2. 瑙ｆ瀽棰勬祴缁撴灉 ---
            # 妫€鏌ユ暟鎹淮搴︽槸鍚︽湁鏁�
            if predictions.shape[1] < 4:  # 鑷冲皯闇€瑕亁ywh鍥涗釜鍧愭爣
                return original_img, []

            # --- 纭畾鏁版嵁鏍煎紡 ---
            has_obj_confidence = self.model_settings["has_obj_confidence"]
            class_offset = self.model_settings["class_offset"]
            
            # 鎻愬彇杈圭晫妗嗗潗鏍�
            boxes_xywh = predictions[:, :4]
            
            # 澶勭悊缃俊搴﹀拰绫诲埆鍒嗘暟
            if has_obj_confidence:
                # YOLOv5鏍煎紡: [x,y,w,h,obj,class1,class2,...]
                obj_scores = predictions[:, 4]
                class_scores = predictions[:, class_offset:]
                # 鏈€缁堢疆淇″害鏄痮bj_score鍜屾渶澶lass_score鐨勪箻绉�
                class_scores_max = np.max(class_scores, axis=1)
                scores = obj_scores * class_scores_max
                class_indices = np.argmax(class_scores, axis=1)
            else:
                # YOLOv8鏍煎紡: [x,y,w,h,class1,class2,...]
                class_scores = predictions[:, 4:]
                # 鏈€缁堢疆淇″害灏辨槸鏈€澶х殑class_score
                scores = np.max(class_scores, axis=1)
                class_indices = np.argmax(class_scores, axis=1)

            # 鏍规嵁缃俊搴﹂槇鍊煎垱寤烘帺鐮侊紝杩涜杩囨护
            mask = scores > self.confidence_threshold
            if not np.any(mask):
                return original_img, []

            # 搴旂敤鎺╃爜锛屽緱鍒版渶缁堢敤浜庡悗缁鐞嗙殑鏁版嵁
            filtered_boxes = boxes_xywh[mask]
            filtered_scores = scores[mask]
            class_indices = class_indices[mask]

            # --- 3. 鍧愭爣杞崲 ---
            img_h, img_w = original_img.shape[:2]
            
            # 妫€鏌ュ潗鏍囨槸鍚︿负褰掍竴鍖� (0-1涔嬮棿)
            if np.max(filtered_boxes) <= 1.0:
                # (cx, cy, w, h) -> (x1, y1, x2, y2)
                cx, cy, w, h = filtered_boxes.T
                x1 = (cx - w / 2) * img_w
                y1 = (cy - h / 2) * img_h
                x2 = (cx + w / 2) * img_w
                y2 = (cy + h / 2) * img_h
                boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)
            else:
                # 濡傛灉鏄儚绱犲潗鏍� (cx, cy, w, h) -> (x1, y1, x2, y2)
                cx, cy, w, h = filtered_boxes.T
                x1 = cx - w / 2
                y1 = cy - h / 2
                x2 = cx + w / 2
                y2 = cy + h / 2
                boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)

            # --- 4. 鎸夌被鍒繘琛岄潪鏋佸ぇ鍊兼姂鍒� (Per-Class NMS) ---
            detections = []
            # 鑾峰彇鎵€鏈変笉閲嶅鐨勭被鍒獻D
            unique_class_ids = np.unique(class_indices)

            # 閬嶅巻姣忎釜绫诲埆ID
            for class_id in unique_class_ids:
                # a. 绛涢€夊嚭褰撳墠绫诲埆鐨勬墍鏈夋娴嬬粨鏋�
                class_mask = (class_indices == class_id)
                class_boxes = boxes_xyxy[class_mask]
                class_scores = filtered_scores[class_mask]

                # 濡傛灉杩欎釜绫诲埆娌℃湁妫€娴嬪埌浠讳綍妗嗭紝灏辫烦鍒颁笅涓€涓被鍒�
                if len(class_boxes) == 0:
                    continue

                # b. 瀵瑰綋鍓嶇被鍒殑妫€娴嬬粨鏋滆繘琛孨MS
                indices = cv2.dnn.NMSBoxes(class_boxes.tolist(), class_scores.tolist(), 
                                          self.confidence_threshold, self.nms_threshold)

                # 濡傛灉NMS涔嬪悗鏈変繚鐣欎笅鏉ョ殑妗�
                if len(indices) > 0:
                    # c. 浠嶯MS缁撴灉涓彁鍙栨渶缁堢殑妫€娴嬩俊鎭�
                    final_boxes_for_class = class_boxes[indices.flatten()]
                    final_scores_for_class = class_scores[indices.flatten()]

                    # 閬嶅巻杩欎釜绫诲埆涓渶缁堜繚鐣欑殑姣忎竴涓
                    for i in range(len(final_boxes_for_class)):
                        x1, y1, x2, y2 = final_boxes_for_class[i]
                        score = final_scores_for_class[i]
                        
                        # 灏嗗潗鏍囪鍓埌鍥惧儚杈圭晫鍐�
                        x = max(0, int(x1))
                        y = max(0, int(y1))
                        w = max(0, int(x2 - x1))
                        h = max(0, int(y2 - y1))
                        
                        # 纭繚绫诲埆ID涓嶄細瓒婄晫
                        if len(self.class_names) == 0:
                            # 濡傛灉娌℃湁绫诲埆鍚嶇О锛屽垯鍒涘缓榛樿鍊�
                            self.class_names = [f"绫诲埆{i}" for i in range(int(max(unique_class_ids)) + 1)]
                            print(f"璀﹀憡: 鑷姩鐢熸垚绫诲埆鍚嶇О: {self.class_names}")
                        
                        # 濡傛灉绫诲埆ID瓒呭嚭鑼冨洿锛屽垯鍔ㄦ€佹墿灞曠被鍒悕绉板垪琛�
                        if int(class_id) >= len(self.class_names):
                            original_len = len(self.class_names)
                            # 娣诲姞瓒冲鐨勬柊绫诲埆鍚嶇О
                            for i in range(original_len, int(class_id) + 1):
                                self.class_names.append(f"绫诲埆{i}")
                            print(f"璀﹀憡: 绫诲埆ID {int(class_id)} 瓒呭嚭鑼冨洿锛屽凡鎵╁睍绫诲埆鍚嶇О鑷� {len(self.class_names)} 涓�")
                            
                        safe_class_id = int(class_id)
                        
                        # 灏嗘暣鐞嗗ソ鐨勪俊鎭坊鍔犲埌缁撴灉鍒楄〃
                        detections.append({
                            "class_id": safe_class_id,
                            "class_name": self.class_names[safe_class_id],
                            "confidence": float(score),
                            "box": [x, y, w, h]
                        })

            # --- 5. 缁樺埗缁撴灉 ---
            result_img = original_img.copy()
            self.draw_detections(result_img, detections)
            
            # 杩斿洖澶勭悊缁撴灉
            return result_img, detections
            
        finally:
            # 濡傛灉搴旂敤浜嗕复鏃惰缃紝鎭㈠鍘熷璁剧疆
            if temp_settings_applied:
                if original_settings:
                    self.model_settings = original_settings
                if original_class_names:
                    self.class_names = original_class_names

    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> None:
        """
        鍦ㄥ浘鍍忎笂缁樺埗妫€娴嬬粨鏋�
        
        Args:
            image: 瑕佺粯鍒剁殑鍥惧儚
            detections: 妫€娴嬬粨鏋滃垪琛�
        """
        colors = {0: (0, 0, 255), 1: (0, 255, 0)}  # BGR
        for det in detections:
            box = det["box"]
            x, y, w, h = box
            class_id = det["class_id"]
            label = f"{det['class_name']}: {det['confidence']:.2f}"
            color = colors.get(class_id, (255, 255, 0))
            
            cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
            cv2.putText(image, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 娴嬭瘯绋嬪簭
if __name__ == "__main__":
    import sys
    
    # 濡傛灉鍛戒护琛屽弬鏁颁腑鎻愪緵浜嗘ā鍨嬭矾寰勶紝浣跨敤瀹�
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = "models/1.onnx"  # 榛樿娴嬭瘯妯″瀷
        
    # 鍒濆鍖栧寮虹増鍚庡鐞嗗櫒锛岃嚜鍔ㄦ帰娴嬫ā鍨嬫牸寮�
    processor = EnhancedDetectionPostProcessor(
        confidence_threshold=0.5,
        nms_threshold=0.4,
        model_path=model_path
    )
    
    print("\n=== 娴嬭瘯鍚庡鐞嗗姛鑳� ===")
    print("1. 鍔犺浇娴嬭瘯鍥剧墖...")
    test_image = cv2.imread("test.jpg")
    
    if test_image is None:
        print("閿欒锛氭棤娉曡鍙栨祴璇曞浘鐗囷紝璇风‘淇濆瓨鍦ㄦ湁鏁堢殑娴嬭瘯鍥剧墖锛�")
    else:
        print("2. 妯℃嫙妯″瀷杈撳嚭...")
        # 鍒涘缓涓€涓亣鐨勬ā鍨嬭緭鍑鸿繘琛屾祴璇�
        fake_output = np.array([[
            [0.2, 0.3, 0.2, 0.3, 0.95, 0.9, 0.1],  # 绫诲埆0锛岄珮缃俊搴�
            [0.7, 0.6, 0.3, 0.4, 0.98, 0.1, 0.9],  # 绫诲埆1锛岄珮缃俊搴�
        ]])
        
        print("3. 鎵ц鍚庡鐞�...")
        result_image, detections = processor.process(fake_output, test_image, (640, 640))
        
        print(f"\n妫€娴嬪埌 {len(detections)} 涓洰鏍�:")
        for i, det in enumerate(detections):
            print(f"  鐩爣 #{i+1}: 绫诲埆='{det['class_name']}', "
                  f"缃俊搴�={det['confidence']:.2f}, 浣嶇疆={det['box']}")
        
        if len(detections) > 0:
            print("\n4. 鏄剧ず缁撴灉鍥惧儚...")
            cv2.imshow("妫€娴嬬粨鏋�", result_image)
            print("璇锋寜浠绘剰閿叧闂獥鍙�...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        else:
            print("鏈娴嬪埌浠讳綍鐩爣锛屾棤娉曟樉绀虹粨鏋溿€�") 