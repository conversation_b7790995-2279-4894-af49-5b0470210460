{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-c9c2119bf54a536147b4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Debug-8c02ce05ba35076a97ea.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Debug-1c8523812bee821e15b5.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-c9c2119bf54a536147b4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Release-98f98af6a50954a11729.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-Release-0cbd0cf641cc31d53770.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-c9c2119bf54a536147b4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-MinSizeRel-0dd7b06cbb6f7e36c0a1.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-MinSizeRel-37a99fca0458275395de.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c9c2119bf54a536147b4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-RelWithDebInfo-2879e85be833fe023167.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "KMBoxTest::@6890427a1f51a3e7e1df", "jsonFile": "target-KMBoxTest-RelWithDebInfo-63a0422ec73d2da553ba.json", "name": "KMBoxTest", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/aim_bot/build", "source": "C:/Users/<USER>/Desktop/aim_bot"}, "version": {"major": 2, "minor": 4}}