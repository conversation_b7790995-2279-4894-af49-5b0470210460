#pragma once

#include <chrono>
#include <algorithm>
#include <functional>
#include <cmath>

/**
 * @brief ???????????
 */
struct Vector2D {
    float x = 0.0f;
    float y = 0.0f;
    
    Vector2D() = default;
    Vector2D(float x, float y) : x(x), y(y) {}
    
    // ????????
    Vector2D operator+(const Vector2D& other) const {
        return Vector2D(x + other.x, y + other.y);
    }
    
    Vector2D operator-(const Vector2D& other) const {
        return Vector2D(x - other.x, y - other.y);
    }
    
    Vector2D operator*(float scalar) const {
        return Vector2D(x * scalar, y * scalar);
    }
    
    // ????????
    float Length() const {
        return sqrt(x * x + y * y);
    }
    
    // ?????
    Vector2D Normalize() const {
        float len = Length();
        if (len > 0.0f) {
            return Vector2D(x / len, y / len);
        }
        return Vector2D(0.0f, 0.0f);
    }
};

/**
 * @brief ????PID??????
 * 
 * ???????????????????????????????????????????????
 * ?????????????????????
 * 
 * PID???????????
 * - P????????????????????????????????
 * - I?????????????????????????????????????????
 * - D??????????????????????????????????????
 */
class PIDController {
public:
    /**
     * @brief PID???????????
     */
    struct PIDConfig {
        // ����PID����
        float kp = 0.8f;                    // ����ϵ�������Ƶ�ǰ������Ӧǿ�ȣ�Խ����ӦԽ�쵫�׳���
        float ki = 0.1f;                    // ����ϵ����������̬���ۻ���ʷ�����в���
        float kd = 0.2f;                    // ΢��ϵ����Ԥ�����仯���ƣ����ٳ�������

        // ������Ʋ���
        float maxOutput = 100.0f;           // ���������ƣ���������ƶ����������������ֹ�ƶ�����
        float minOutput = 1.0f;             // ��С�����ֵ��С�ڴ�ֵ������������ԣ�����΢С����
        float maxIntegral = 50.0f;          // �����޷�����ֹ�����������ۻ�����ϵͳ���ȶ�
        float deadZone = 2.0f;              // ������Χ�����С�ڴ�ֵʱ���������������ȶ���

        // �߼��Ż�����
        bool enableIntegralDecay = true;    // ���û���˥����ÿ�μ����������Զ�˥������ֹ����
        float integralDecayRate = 0.95f;    // ����˥���ʣ�ÿ��˥���ı�����ԽС˥��Խ��
        bool enableDerivativeFilter = true; // ����΢���˲�����΢������е�ͨ�˲�����������
        float derivativeFilterAlpha = 0.7f; // ΢���˲�ϵ�����˲�ǿ�ȣ�Խ��Խƽ������ӦԽ��

        // ����Ӧ���Ʋ���
        bool enableAdaptiveGain = false;    // ��������Ӧ���棺��������С��̬��������
        float adaptiveGainFactor = 1.5f;    // ����Ӧ�������ӣ������ʱ������Ŵ���
        float adaptiveThreshold = 20.0f;    // ����Ӧ��ֵ��������������ʱ��������Ŵ�
    };

public:
    /**
     * @brief ??????
     * @param config PID????????
     */
    explicit PIDController(const PIDConfig& config = PIDConfig());

    /**
     * @brief ????????
     */
    ~PIDController() = default;

    /**
     * @brief ????PID??????????????
     * @param error ???????????????????????
     * @return ??????????????
     */
    Vector2D Calculate(const Vector2D& error);

    /**
     * @brief ????PID????????
     */
    void Reset();

    /**
     * @brief ????PID????
     * @param config ???????????
     */
    void UpdateConfig(const PIDConfig& config);

    /**
     * @brief ??????????
     * @return ???PID????
     */
    PIDConfig GetConfig() const;

    /**
     * @brief ??????????
     */
    struct DebugInfo {
        Vector2D lastError;         // ??????
        Vector2D integral;          // ???????
        Vector2D derivative;        // ????
        Vector2D pOutput;           // P?????
        Vector2D iOutput;           // I?????
        Vector2D dOutput;           // D?????
        Vector2D totalOutput;       // ?????
        float deltaTime;            // ?????
        bool inDeadZone;           // ???????????
    };

    /**
     * @brief ??????????
     * @return ???????????
     */
    DebugInfo GetDebugInfo() const;

    /**
     * @brief ???????????
     * @param callback ?????��???????????????
     */
    void SetTargetReachedCallback(std::function<void()> callback);

private:
    // PID????
    PIDConfig m_config;
    
    // PID??????
    Vector2D m_lastError;                   // ??????
    Vector2D m_integral;                    // ???????
    Vector2D m_derivative;                  // ????
    Vector2D m_filteredDerivative;          // ??????????
    
    // ??????
    std::chrono::high_resolution_clock::time_point m_lastTime;
    bool m_firstRun;                        // ?????????????
    
    // ???????
    mutable DebugInfo m_debugInfo;
    
    // ???????
    std::function<void()> m_targetReachedCallback;

    // ???????
    float CalculateDeltaTime();
    Vector2D ApplyLimits(const Vector2D& output);
    Vector2D ApplyDeadZone(const Vector2D& error);
    Vector2D ApplyAdaptiveGain(const Vector2D& output, const Vector2D& error);
    void UpdateIntegral(const Vector2D& error, float deltaTime);
    void UpdateDerivative(const Vector2D& error, float deltaTime);
    void UpdateDebugInfo(const Vector2D& error, const Vector2D& pOut, 
                        const Vector2D& iOut, const Vector2D& dOut, 
                        const Vector2D& totalOut, float deltaTime);
};


