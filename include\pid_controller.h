#pragma once

#include <chrono>
#include <algorithm>

/**
 * @brief 二维向量结构体
 */
struct Vector2D {
    float x = 0.0f;
    float y = 0.0f;
    
    Vector2D() = default;
    Vector2D(float x, float y) : x(x), y(y) {}
    
    // 向量运算
    Vector2D operator+(const Vector2D& other) const {
        return Vector2D(x + other.x, y + other.y);
    }
    
    Vector2D operator-(const Vector2D& other) const {
        return Vector2D(x - other.x, y - other.y);
    }
    
    Vector2D operator*(float scalar) const {
        return Vector2D(x * scalar, y * scalar);
    }
    
    // 向量长度
    float Length() const {
        return sqrt(x * x + y * y);
    }
    
    // 归一化
    Vector2D Normalize() const {
        float len = Length();
        if (len > 0.0f) {
            return Vector2D(x / len, y / len);
        }
        return Vector2D(0.0f, 0.0f);
    }
};

/**
 * @brief 简易PID控制器
 * 
 * 用于自瞄系统的鼠标移动控制，输入是准星与目标之间的误差距离，
 * 输出是鼠标应该移动的矢量。
 * 
 * PID控制器原理：
 * - P（比例）：根据当前误差大小决定输出强度
 * - I（积分）：根据历史误差累积决定输出，消除稳态误差
 * - D（微分）：根据误差变化率决定输出，减少超调和震荡
 */
class PIDController {
public:
    /**
     * @brief PID参数配置结构体
     */
    struct PIDConfig {
        // 基本PID参数
        float kp = 0.8f;                    // 比例系数
        float ki = 0.1f;                    // 积分系数
        float kd = 0.2f;                    // 微分系数
        
        // 限制参数
        float maxOutput = 100.0f;           // 最大输出限制（像素）
        float minOutput = 1.0f;             // 最小输出阈值（像素）
        float maxIntegral = 50.0f;          // 积分限幅
        float deadZone = 2.0f;              // 死区范围（像素）
        
        // 高级参数
        bool enableIntegralDecay = true;    // 是否启用积分衰减
        float integralDecayRate = 0.95f;    // 积分衰减率
        bool enableDerivativeFilter = true; // 是否启用微分滤波
        float derivativeFilterAlpha = 0.7f; // 微分滤波系数
        
        // 自适应参数
        bool enableAdaptiveGain = false;    // 是否启用自适应增益
        float adaptiveGainFactor = 1.5f;    // 自适应增益因子
        float adaptiveThreshold = 20.0f;    // 自适应阈值
    };

public:
    /**
     * @brief 构造函数
     * @param config PID配置参数
     */
    explicit PIDController(const PIDConfig& config = PIDConfig());

    /**
     * @brief 析构函数
     */
    ~PIDController() = default;

    /**
     * @brief 计算PID输出（核心函数）
     * @param error 当前误差向量（准星到目标的距离）
     * @return 鼠标应该移动的矢量
     */
    Vector2D Calculate(const Vector2D& error);

    /**
     * @brief 重置PID控制器状态
     */
    void Reset();

    /**
     * @brief 更新PID配置
     * @param config 新的配置参数
     */
    void UpdateConfig(const PIDConfig& config);

    /**
     * @brief 获取当前配置
     * @return 当前PID配置
     */
    PIDConfig GetConfig() const;

    /**
     * @brief 获取调试信息
     */
    struct DebugInfo {
        Vector2D lastError;         // 上次误差
        Vector2D integral;          // 积分累积
        Vector2D derivative;        // 微分值
        Vector2D pOutput;           // P项输出
        Vector2D iOutput;           // I项输出
        Vector2D dOutput;           // D项输出
        Vector2D totalOutput;       // 总输出
        float deltaTime;            // 时间间隔
        bool inDeadZone;           // 是否在死区内
    };

    /**
     * @brief 获取调试信息
     * @return 调试信息结构体
     */
    DebugInfo GetDebugInfo() const;

    /**
     * @brief 设置目标到达回调
     * @param callback 当误差小于死区时的回调函数
     */
    void SetTargetReachedCallback(std::function<void()> callback);

private:
    // PID配置
    PIDConfig m_config;
    
    // PID状态变量
    Vector2D m_lastError;                   // 上次误差
    Vector2D m_integral;                    // 积分累积
    Vector2D m_derivative;                  // 微分值
    Vector2D m_filteredDerivative;          // 滤波后的微分值
    
    // 时间管理
    std::chrono::high_resolution_clock::time_point m_lastTime;
    bool m_firstRun;                        // 是否是第一次运行
    
    // 调试信息
    mutable DebugInfo m_debugInfo;
    
    // 回调函数
    std::function<void()> m_targetReachedCallback;

    // 私有方法
    float CalculateDeltaTime();
    Vector2D ApplyLimits(const Vector2D& output);
    Vector2D ApplyDeadZone(const Vector2D& error);
    Vector2D ApplyAdaptiveGain(const Vector2D& output, const Vector2D& error);
    void UpdateIntegral(const Vector2D& error, float deltaTime);
    void UpdateDerivative(const Vector2D& error, float deltaTime);
    void UpdateDebugInfo(const Vector2D& error, const Vector2D& pOut, 
                        const Vector2D& iOut, const Vector2D& dOut, 
                        const Vector2D& totalOut, float deltaTime);
};

/**
 * @brief PID控制器工厂类
 * 提供预设的PID配置
 */
class PIDControllerFactory {
public:
    /**
     * @brief 创建快速响应的PID控制器（适合近距离目标）
     */
    static PIDController CreateFastResponse();

    /**
     * @brief 创建平滑响应的PID控制器（适合远距离目标）
     */
    static PIDController CreateSmoothResponse();

    /**
     * @brief 创建精确响应的PID控制器（适合精确瞄准）
     */
    static PIDController CreatePreciseResponse();

    /**
     * @brief 创建自适应PID控制器（自动调整参数）
     */
    static PIDController CreateAdaptiveResponse();

    /**
     * @brief 从配置字符串创建PID控制器
     * @param configStr 配置字符串（简化格式："kp,ki,kd"）
     */
    static PIDController CreateFromString(const std::string& configStr);
};
