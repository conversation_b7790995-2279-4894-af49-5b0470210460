#pragma once

#include <chrono>
#include <algorithm>
#include <functional>
#include <cmath>

/**
 * @brief ��ά�����ṹ��
 */
struct Vector2D {
    float x = 0.0f;
    float y = 0.0f;
    
    Vector2D() = default;
    Vector2D(float x, float y) : x(x), y(y) {}
    
    // ��������
    Vector2D operator+(const Vector2D& other) const {
        return Vector2D(x + other.x, y + other.y);
    }
    
    Vector2D operator-(const Vector2D& other) const {
        return Vector2D(x - other.x, y - other.y);
    }
    
    Vector2D operator*(float scalar) const {
        return Vector2D(x * scalar, y * scalar);
    }
    
    // ��������
    float Length() const {
        return sqrt(x * x + y * y);
    }
    
    // ��һ��
    Vector2D Normalize() const {
        float len = Length();
        if (len > 0.0f) {
            return Vector2D(x / len, y / len);
        }
        return Vector2D(0.0f, 0.0f);
    }
};

/**
 * @brief ����PID������
 * 
 * ��������ϵͳ������ƶ����ƣ�������׼����Ŀ��֮��������룬
 * ��������Ӧ���ƶ���ʸ����
 * 
 * PID������ԭ����
 * - P�������������ݵ�ǰ����С�������ǿ��
 * - I�����֣���������ʷ����ۻ����������������̬���
 * - D��΢�֣����������仯�ʾ�����������ٳ�������
 */
class PIDController {
public:
    /**
     * @brief PID�������ýṹ��
     */
    struct PIDConfig {
        // ����PID����
        float kp = 0.8f;                    // ����ϵ��
        float ki = 0.1f;                    // ����ϵ��
        float kd = 0.2f;                    // ΢��ϵ��
        
        // ���Ʋ���
        float maxOutput = 100.0f;           // ���������ƣ����أ�
        float minOutput = 1.0f;             // ��С�����ֵ�����أ�
        float maxIntegral = 50.0f;          // �����޷�
        float deadZone = 2.0f;              // ������Χ�����أ�
        
        // �߼�����
        bool enableIntegralDecay = true;    // �Ƿ����û���˥��
        float integralDecayRate = 0.95f;    // ����˥����
        bool enableDerivativeFilter = true; // �Ƿ�����΢���˲�
        float derivativeFilterAlpha = 0.7f; // ΢���˲�ϵ��
        
        // ����Ӧ����
        bool enableAdaptiveGain = false;    // �Ƿ���������Ӧ����
        float adaptiveGainFactor = 1.5f;    // ����Ӧ��������
        float adaptiveThreshold = 20.0f;    // ����Ӧ��ֵ
    };

public:
    /**
     * @brief ���캯��
     * @param config PID���ò���
     */
    explicit PIDController(const PIDConfig& config = PIDConfig());

    /**
     * @brief ��������
     */
    ~PIDController() = default;

    /**
     * @brief ����PID��������ĺ�����
     * @param error ��ǰ���������׼�ǵ�Ŀ��ľ��룩
     * @return ���Ӧ���ƶ���ʸ��
     */
    Vector2D Calculate(const Vector2D& error);

    /**
     * @brief ����PID������״̬
     */
    void Reset();

    /**
     * @brief ����PID����
     * @param config �µ����ò���
     */
    void UpdateConfig(const PIDConfig& config);

    /**
     * @brief ��ȡ��ǰ����
     * @return ��ǰPID����
     */
    PIDConfig GetConfig() const;

    /**
     * @brief ��ȡ������Ϣ
     */
    struct DebugInfo {
        Vector2D lastError;         // �ϴ����
        Vector2D integral;          // �����ۻ�
        Vector2D derivative;        // ΢��ֵ
        Vector2D pOutput;           // P�����
        Vector2D iOutput;           // I�����
        Vector2D dOutput;           // D�����
        Vector2D totalOutput;       // �����
        float deltaTime;            // ʱ����
        bool inDeadZone;           // �Ƿ���������
    };

    /**
     * @brief ��ȡ������Ϣ
     * @return ������Ϣ�ṹ��
     */
    DebugInfo GetDebugInfo() const;

    /**
     * @brief 设置目标到达回调
     * @param callback 当误差小于死区时的回调函数
     */
    void SetTargetReachedCallback(std::function<void()> callback);

private:
    // PID����
    PIDConfig m_config;
    
    // PID״̬����
    Vector2D m_lastError;                   // �ϴ����
    Vector2D m_integral;                    // �����ۻ�
    Vector2D m_derivative;                  // ΢��ֵ
    Vector2D m_filteredDerivative;          // �˲����΢��ֵ
    
    // ʱ�����
    std::chrono::high_resolution_clock::time_point m_lastTime;
    bool m_firstRun;                        // �Ƿ��ǵ�һ������
    
    // ������Ϣ
    mutable DebugInfo m_debugInfo;
    
    // �ص�����
    std::function<void()> m_targetReachedCallback;

    // ˽�з���
    float CalculateDeltaTime();
    Vector2D ApplyLimits(const Vector2D& output);
    Vector2D ApplyDeadZone(const Vector2D& error);
    Vector2D ApplyAdaptiveGain(const Vector2D& output, const Vector2D& error);
    void UpdateIntegral(const Vector2D& error, float deltaTime);
    void UpdateDerivative(const Vector2D& error, float deltaTime);
    void UpdateDebugInfo(const Vector2D& error, const Vector2D& pOut, 
                        const Vector2D& iOut, const Vector2D& dOut, 
                        const Vector2D& totalOut, float deltaTime);
};

/**
 * @brief PID������������
 * �ṩԤ���PID����
 */
class PIDControllerFactory {
public:
    /**
     * @brief ����������Ӧ��PID���������ʺϽ�����Ŀ�꣩
     */
    static PIDController CreateFastResponse();

    /**
     * @brief ����ƽ����Ӧ��PID���������ʺ�Զ����Ŀ�꣩
     */
    static PIDController CreateSmoothResponse();

    /**
     * @brief ������ȷ��Ӧ��PID���������ʺϾ�ȷ��׼��
     */
    static PIDController CreatePreciseResponse();

    /**
     * @brief ��������ӦPID���������Զ�����������
     */
    static PIDController CreateAdaptiveResponse();

    /**
     * @brief �������ַ�������PID������
     * @param configStr �����ַ������򻯸�ʽ��"kp,ki,kd"��
     */
    static PIDController CreateFromString(const std::string& configStr);
};
