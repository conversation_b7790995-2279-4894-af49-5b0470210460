#include <iostream>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <Windows.h>
#include "pid_controller.h"

/**
 * @brief PID控制器测试程序
 * 
 * 这个程序用于测试PID控制器的各种功能，包括：
 * 1. 不同预设配置的测试
 * 2. 模拟目标跟踪场景
 * 3. 参数调优和性能分析
 * 4. 实时调试信息显示
 */

void PrintMenu() {
    std::cout << "\n=== PID控制器测试程序 ===" << std::endl;
    std::cout << "1. 快速响应模式测试" << std::endl;
    std::cout << "2. 平滑响应模式测试" << std::endl;
    std::cout << "3. 精确响应模式测试" << std::endl;
    std::cout << "4. 自适应响应模式测试" << std::endl;
    std::cout << "5. 自定义参数测试" << std::endl;
    std::cout << "6. 模拟目标跟踪测试" << std::endl;
    std::cout << "7. 参数调优模式" << std::endl;
    std::cout << "8. 性能基准测试" << std::endl;
    std::cout << "0. 退出程序" << std::endl;
    std::cout << "请选择测试模式: ";
}

void PrintDebugInfo(const PIDController::DebugInfo& debug) {
    std::cout << std::fixed << std::setprecision(2);
    std::cout << "=== PID调试信息 ===" << std::endl;
    std::cout << "误差: (" << debug.lastError.x << ", " << debug.lastError.y << ")" << std::endl;
    std::cout << "P项: (" << debug.pOutput.x << ", " << debug.pOutput.y << ")" << std::endl;
    std::cout << "I项: (" << debug.iOutput.x << ", " << debug.iOutput.y << ")" << std::endl;
    std::cout << "D项: (" << debug.dOutput.x << ", " << debug.dOutput.y << ")" << std::endl;
    std::cout << "输出: (" << debug.totalOutput.x << ", " << debug.totalOutput.y << ")" << std::endl;
    std::cout << "积分: (" << debug.integral.x << ", " << debug.integral.y << ")" << std::endl;
    std::cout << "微分: (" << debug.derivative.x << ", " << debug.derivative.y << ")" << std::endl;
    std::cout << "时间间隔: " << debug.deltaTime * 1000.0f << "ms" << std::endl;
    std::cout << "死区状态: " << (debug.inDeadZone ? "是" : "否") << std::endl;
    std::cout << "===================" << std::endl;
}

void TestPIDController(PIDController& pid, const std::string& testName) {
    std::cout << "\n=== " << testName << " ===" << std::endl;
    
    // 模拟目标位置序列（从远到近）
    std::vector<Vector2D> targets = {
        Vector2D(100.0f, 50.0f),   // 远距离目标
        Vector2D(50.0f, 30.0f),    // 中距离目标
        Vector2D(20.0f, 15.0f),    // 近距离目标
        Vector2D(5.0f, 3.0f),      // 很近的目标
        Vector2D(1.0f, 1.0f)       // 精确目标
    };
    
    Vector2D currentPos(0.0f, 0.0f);  // 当前准星位置
    
    for (size_t i = 0; i < targets.size(); i++) {
        std::cout << "\n--- 目标 " << (i + 1) << ": (" 
                  << targets[i].x << ", " << targets[i].y << ") ---" << std::endl;
        
        int steps = 0;
        const int maxSteps = 20;  // 最大迭代次数
        
        while (steps < maxSteps) {
            // 计算误差（目标位置 - 当前位置）
            Vector2D error = targets[i] - currentPos;
            
            // 如果误差很小，认为到达目标
            if (error.Length() < 2.0f) {
                std::cout << "✓ 目标到达！步数: " << steps << std::endl;
                break;
            }
            
            // 计算PID输出
            Vector2D output = pid.Calculate(error);
            
            // 模拟鼠标移动（更新当前位置）
            currentPos = currentPos + output;
            
            // 显示调试信息（每5步显示一次）
            if (steps % 5 == 0) {
                std::cout << "步骤 " << steps << ": 位置(" << currentPos.x << ", " << currentPos.y 
                         << ") 误差(" << error.x << ", " << error.y 
                         << ") 输出(" << output.x << ", " << output.y << ")" << std::endl;
            }
            
            steps++;
            Sleep(50);  // 模拟50ms的控制周期
        }
        
        if (steps >= maxSteps) {
            std::cout << "✗ 未能到达目标，最大步数已达到" << std::endl;
        }
        
        // 重置PID状态准备下一个目标
        pid.Reset();
        Sleep(200);
    }
}

void CustomParameterTest() {
    std::cout << "\n=== 自定义参数测试 ===" << std::endl;
    
    PIDController::PIDConfig config;
    
    std::cout << "请输入PID参数:" << std::endl;
    std::cout << "比例系数 Kp (默认0.8): ";
    std::cin >> config.kp;
    
    std::cout << "积分系数 Ki (默认0.1): ";
    std::cin >> config.ki;
    
    std::cout << "微分系数 Kd (默认0.2): ";
    std::cin >> config.kd;
    
    std::cout << "最大输出 (默认100): ";
    std::cin >> config.maxOutput;
    
    std::cout << "死区范围 (默认2.0): ";
    std::cin >> config.deadZone;
    
    PIDController pid(config);
    TestPIDController(pid, "自定义参数测试");
}

void SimulateTargetTracking() {
    std::cout << "\n=== 模拟目标跟踪测试 ===" << std::endl;
    
    auto pid = PIDControllerFactory::CreateAdaptiveResponse();
    
    // 设置目标到达回调
    pid.SetTargetReachedCallback([]() {
        std::cout << "🎯 目标锁定！" << std::endl;
    });
    
    Vector2D currentPos(0.0f, 0.0f);
    
    // 模拟移动目标
    std::vector<Vector2D> movingTarget = {
        Vector2D(80.0f, 60.0f),
        Vector2D(75.0f, 55.0f),
        Vector2D(70.0f, 50.0f),
        Vector2D(65.0f, 45.0f),
        Vector2D(60.0f, 40.0f),
        Vector2D(55.0f, 35.0f),
        Vector2D(50.0f, 30.0f)
    };
    
    std::cout << "开始跟踪移动目标..." << std::endl;
    
    for (size_t i = 0; i < movingTarget.size(); i++) {
        Vector2D targetPos = movingTarget[i];
        Vector2D error = targetPos - currentPos;
        
        std::cout << "\n帧 " << i << ": 目标(" << targetPos.x << ", " << targetPos.y << ")" << std::endl;
        
        // 计算PID输出
        Vector2D output = pid.Calculate(error);
        
        // 更新位置
        currentPos = currentPos + output;
        
        // 显示详细信息
        auto debug = pid.GetDebugInfo();
        std::cout << "当前位置: (" << currentPos.x << ", " << currentPos.y << ")" << std::endl;
        std::cout << "误差距离: " << error.Length() << " 像素" << std::endl;
        std::cout << "输出强度: " << output.Length() << " 像素" << std::endl;
        
        Sleep(100);  // 模拟100ms帧间隔
    }
}

void ParameterTuning() {
    std::cout << "\n=== 参数调优模式 ===" << std::endl;
    
    PIDController::PIDConfig config;
    config.kp = 0.8f;
    config.ki = 0.1f;
    config.kd = 0.2f;
    
    PIDController pid(config);
    
    Vector2D testTarget(50.0f, 30.0f);
    Vector2D currentPos(0.0f, 0.0f);
    
    std::cout << "实时参数调优 - 按ESC退出" << std::endl;
    std::cout << "目标位置: (" << testTarget.x << ", " << testTarget.y << ")" << std::endl;
    
    while (true) {
        // 检查ESC键
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
        
        // 计算误差和输出
        Vector2D error = testTarget - currentPos;
        Vector2D output = pid.Calculate(error);
        
        // 更新位置
        currentPos = currentPos + output * 0.1f;  // 减慢移动速度便于观察
        
        // 显示实时信息
        system("cls");  // 清屏
        std::cout << "=== 实时PID调优 ===" << std::endl;
        std::cout << "当前配置: Kp=" << config.kp << ", Ki=" << config.ki << ", Kd=" << config.kd << std::endl;
        std::cout << "当前位置: (" << std::fixed << std::setprecision(1) 
                  << currentPos.x << ", " << currentPos.y << ")" << std::endl;
        std::cout << "目标位置: (" << testTarget.x << ", " << testTarget.y << ")" << std::endl;
        std::cout << "误差距离: " << error.Length() << " 像素" << std::endl;
        std::cout << "输出强度: " << output.Length() << " 像素" << std::endl;
        
        auto debug = pid.GetDebugInfo();
        PrintDebugInfo(debug);
        
        std::cout << "\n按键调整参数:" << std::endl;
        std::cout << "Q/A - 调整Kp  W/S - 调整Ki  E/D - 调整Kd  R - 重置位置  ESC - 退出" << std::endl;
        
        // 检查参数调整按键
        if (GetAsyncKeyState('Q') & 0x8000) {
            config.kp += 0.1f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('A') & 0x8000) {
            config.kp -= 0.1f;
            if (config.kp < 0.0f) config.kp = 0.0f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('W') & 0x8000) {
            config.ki += 0.05f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('S') & 0x8000) {
            config.ki -= 0.05f;
            if (config.ki < 0.0f) config.ki = 0.0f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('E') & 0x8000) {
            config.kd += 0.05f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('D') & 0x8000) {
            config.kd -= 0.05f;
            if (config.kd < 0.0f) config.kd = 0.0f;
            pid.UpdateConfig(config);
            Sleep(100);
        }
        if (GetAsyncKeyState('R') & 0x8000) {
            currentPos = Vector2D(0.0f, 0.0f);
            pid.Reset();
            Sleep(200);
        }
        
        Sleep(50);  // 控制刷新率
    }
}

void BenchmarkTest() {
    std::cout << "\n=== 性能基准测试 ===" << std::endl;
    
    auto pid = PIDControllerFactory::CreateFastResponse();
    
    const int iterations = 10000;
    Vector2D error(50.0f, 30.0f);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; i++) {
        Vector2D output = pid.Calculate(error);
        error = error * 0.99f;  // 模拟误差逐渐减小
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    
    std::cout << "执行 " << iterations << " 次PID计算" << std::endl;
    std::cout << "总耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "平均耗时: " << duration.count() / iterations << " 微秒/次" << std::endl;
    std::cout << "理论最大帧率: " << 1000000.0f / (duration.count() / iterations) << " FPS" << std::endl;
}

int main() {
    std::cout << "=== PID控制器测试程序 ===" << std::endl;
    std::cout << "这个程序用于测试和调优PID控制器参数" << std::endl;
    std::cout << "适用于自瞄系统的鼠标移动控制" << std::endl;
    
    bool running = true;
    int choice;
    
    while (running) {
        PrintMenu();
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                auto pid = PIDControllerFactory::CreateFastResponse();
                TestPIDController(pid, "快速响应模式测试");
                break;
            }
            case 2: {
                auto pid = PIDControllerFactory::CreateSmoothResponse();
                TestPIDController(pid, "平滑响应模式测试");
                break;
            }
            case 3: {
                auto pid = PIDControllerFactory::CreatePreciseResponse();
                TestPIDController(pid, "精确响应模式测试");
                break;
            }
            case 4: {
                auto pid = PIDControllerFactory::CreateAdaptiveResponse();
                TestPIDController(pid, "自适应响应模式测试");
                break;
            }
            case 5:
                CustomParameterTest();
                break;
            case 6:
                SimulateTargetTracking();
                break;
            case 7:
                ParameterTuning();
                break;
            case 8:
                BenchmarkTest();
                break;
            case 0:
                running = false;
                std::cout << "退出程序..." << std::endl;
                break;
            default:
                std::cout << "无效选择，请重新输入" << std::endl;
                break;
        }
        
        if (choice != 0) {
            std::cout << "\n按Enter键继续...";
            std::cin.ignore();
            std::cin.get();
        }
    }
    
    return 0;
}
