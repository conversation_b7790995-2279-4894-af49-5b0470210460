#define NOMINMAX
#include "inference_engine.h"
#include <iostream>
#include <algorithm>
#include <chrono>
#include <cmath>

InferenceEngine::InferenceEngine() : lastInferenceTime(0.0) {
}

InferenceEngine::~InferenceEngine() {
    Cleanup();
}

bool InferenceEngine::Initialize(ID3D11Device* device, const InferenceConfig& config) {
    this->config = config;
    this->d3dDevice = device;
    device->GetImmediateContext(&d3dContext);
    
    std::cout << "正在初始化推理引擎..." << std::endl;
    std::cout << "模型: " << config.modelPath << std::endl;
    std::cout << "输入尺寸: " << config.inputWidth << "x" << config.inputHeight << "x" << config.inputChannels << std::endl;

    // 初始化ONNX Runtime
    if (!InitializeOnnxRuntime()) {
        std::cout << "ONNX Runtime初始化失败" << std::endl;
        return false;
    }

    // 创建用于CPU访问的暂存纹理
    if (!CreateStagingTexture()) {
        std::cout << "暂存纹理创建失败" << std::endl;
        return false;
    }

    std::cout << "推理引擎初始化成功" << std::endl;
    return true;
}

bool InferenceEngine::InitializeOnnxRuntime() {
    try {
        // Create ONNX Runtime environment
        ortEnv = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "InferenceEngine");
        
        // Create session options
        Ort::SessionOptions sessionOptions;
        sessionOptions.SetIntraOpNumThreads(1);
        sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);
        
        // Add DirectML provider for GPU acceleration
        if (config.useGPU) {
            std::cout << "使用DirectML提供程序进行GPU加速" << std::endl;
            Ort::ThrowOnError(OrtSessionOptionsAppendExecutionProvider_DML(sessionOptions, 0));
        }
        
        // Convert model path to wide string
        std::wstring wModelPath(config.modelPath.begin(), config.modelPath.end());
        
        // Create session
        ortSession = std::make_unique<Ort::Session>(*ortEnv, wModelPath.c_str(), sessionOptions);
        
        // Get input/output information
        Ort::AllocatorWithDefaultOptions allocator;
        
        // Input info
        size_t numInputNodes = ortSession->GetInputCount();
        if (numInputNodes != 1) {
            std::cout << "模型应该有且仅有1个输入，实际得到 " << numInputNodes << " 个" << std::endl;
            return false;
        }
        
        // Use hardcoded input name for PUBGV8_320 model
        inputNames.push_back("images");
        Ort::TypeInfo inputTypeInfo = ortSession->GetInputTypeInfo(0);
        auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
        inputShape = inputTensorInfo.GetShape();
        
        std::cout << "输入: " << inputNames[0] << " 形状: [";
        for (size_t i = 0; i < inputShape.size(); ++i) {
            std::cout << inputShape[i];
            if (i < inputShape.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
        
        // Output info
        size_t numOutputNodes = ortSession->GetOutputCount();
        if (numOutputNodes != 1) {
            std::cout << "模型应该有且仅有1个输出，实际得到 " << numOutputNodes << " 个" << std::endl;
            return false;
        }
        
        // Use hardcoded output name for PUBGV8_320 model
        outputNames.push_back("output0");
        Ort::TypeInfo outputTypeInfo = ortSession->GetOutputTypeInfo(0);
        auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
        outputShape = outputTensorInfo.GetShape();
        
        std::cout << "输出: " << outputNames[0] << " 形状: [";
        for (size_t i = 0; i < outputShape.size(); ++i) {
            std::cout << outputShape[i];
            if (i < outputShape.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
        
        // Verify shapes match expected
        if (inputShape.size() != 4 || inputShape[1] != 3 || inputShape[2] != 320 || inputShape[3] != 320) {
            std::cout << "输入形状不符合预期。期望 [1,3,320,320]" << std::endl;
            return false;
        }
        
        if (outputShape.size() != 3 || outputShape[1] != 6 || outputShape[2] != 2100) {
            std::cout << "输出形状不符合预期。期望 [1,6,2100]" << std::endl;
            return false;
        }
        
        // Create memory info
        memoryInfo = std::make_unique<Ort::MemoryInfo>(Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault));
        
        return true;
    }
    catch (const Ort::Exception& e) {
        std::cout << "ONNX Runtime错误: " << e.what() << std::endl;
        return false;
    }
}

bool InferenceEngine::CreateStagingTexture() {
    D3D11_TEXTURE2D_DESC stagingDesc = {};
    stagingDesc.Width = config.inputWidth;
    stagingDesc.Height = config.inputHeight;
    stagingDesc.MipLevels = 1;
    stagingDesc.ArraySize = 1;
    stagingDesc.Format = DXGI_FORMAT_R32G32B32A32_FLOAT;  // Match preprocessor output
    stagingDesc.SampleDesc.Count = 1;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDesc.BindFlags = 0;
    
    HRESULT hr = d3dDevice->CreateTexture2D(&stagingDesc, nullptr, &stagingTexture);
    if (FAILED(hr)) {
        std::cout << "暂存纹理创建失败: 0x" << std::hex << hr << std::endl;
        return false;
    }
    
    return true;
}

bool InferenceEngine::RunInference(ID3D11Texture2D* preprocessedTexture, std::vector<Detection>& detections) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    detections.clear();
    
    try {
        // Convert texture to tensor data
        std::vector<float> tensorData;
        if (!ConvertTextureToTensor(preprocessedTexture, tensorData)) {
            return false;
        }
        
        // Create input tensor
        Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
            *memoryInfo, tensorData.data(), tensorData.size(),
            inputShape.data(), inputShape.size()
        );
        
        // Run inference
        auto outputTensors = ortSession->Run(Ort::RunOptions{nullptr}, 
                                           inputNames.data(), &inputTensor, 1,
                                           outputNames.data(), 1);
        
        // Process output
        float* outputData = outputTensors[0].GetTensorMutableData<float>();
        if (!ProcessModelOutput(outputData, detections)) {
            return false;
        }
        
        // Apply Non-Maximum Suppression
        ApplyNMS(detections);
        
        auto endTime = std::chrono::high_resolution_clock::now();
        lastInferenceTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        return true;
    }
    catch (const Ort::Exception& e) {
        std::cout << "推理错误: " << e.what() << std::endl;
        return false;
    }
}

bool InferenceEngine::ConvertTextureToTensor(ID3D11Texture2D* texture, std::vector<float>& tensorData) {
    // Copy GPU texture to staging texture
    d3dContext->CopyResource(stagingTexture.Get(), texture);
    
    // Map staging texture for CPU access
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = d3dContext->Map(stagingTexture.Get(), 0, D3D11_MAP_READ, 0, &mappedResource);
    if (FAILED(hr)) {
        std::cout << "暂存纹理映射失败: 0x" << std::hex << hr << std::endl;
        return false;
    }
    
    // Convert RGBA float data to RGB tensor format (CHW)
    const int width = config.inputWidth;
    const int height = config.inputHeight;
    const int channels = config.inputChannels;
    
    tensorData.resize(channels * height * width);
    
    const float* srcData = reinterpret_cast<const float*>(mappedResource.pData);
    const int srcPitch = mappedResource.RowPitch / sizeof(float);
    
    // Convert from RGBA interleaved to RGB planar (CHW format)
    for (int c = 0; c < channels; ++c) {
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int srcIdx = y * srcPitch + x * 4 + c;  // RGBA format
                int dstIdx = c * height * width + y * width + x;  // CHW format
                tensorData[dstIdx] = srcData[srcIdx];
            }
        }
    }
    
    d3dContext->Unmap(stagingTexture.Get(), 0);
    return true;
}

bool InferenceEngine::ProcessModelOutput(const float* outputData, std::vector<Detection>& detections) {
    // Output format: [1, 6, 2100] -> [batch, (x,y,w,h,conf_head,conf_body), num_detections]
    const int numDetections = 2100;
    const int numParams = 6;

    for (int i = 0; i < numDetections; ++i) {
        // Extract detection parameters
        float x = outputData[0 * numDetections + i];      // Center X
        float y = outputData[1 * numDetections + i];      // Center Y
        float w = outputData[2 * numDetections + i];      // Width
        float h = outputData[3 * numDetections + i];      // Height
        float conf_head = outputData[4 * numDetections + i];  // Head confidence
        float conf_body = outputData[5 * numDetections + i];  // Body confidence

        // Determine class and max confidence
        float maxConf = std::max(conf_head, conf_body);
        int classId = (conf_head > conf_body) ? 0 : 1;  // 0=head, 1=body

        // Filter by confidence threshold
        if (maxConf >= config.confidenceThreshold) {
            Detection det;
            det.x = x;
            det.y = y;
            det.width = w;
            det.height = h;
            det.confidence_head = conf_head;
            det.confidence_body = conf_body;
            det.classId = classId;
            det.confidence = maxConf;

            detections.push_back(det);
        }
    }

    std::cout << "\r发现 " << detections.size() << " 个检测结果，置信度阈值 " << config.confidenceThreshold << std::endl;
    return true;
}

void InferenceEngine::ApplyNMS(std::vector<Detection>& detections, float nmsThreshold) {
    if (detections.empty()) return;

    // Sort by confidence (descending)
    std::sort(detections.begin(), detections.end(),
              [](const Detection& a, const Detection& b) {
                  return a.confidence > b.confidence;
              });

    std::vector<bool> suppressed(detections.size(), false);

    for (size_t i = 0; i < detections.size(); ++i) {
        if (suppressed[i]) continue;

        for (size_t j = i + 1; j < detections.size(); ++j) {
            if (suppressed[j]) continue;

            // Only apply NMS within the same class
            if (detections[i].classId == detections[j].classId) {
                float iou = CalculateIoU(detections[i], detections[j]);
                if (iou > nmsThreshold) {
                    suppressed[j] = true;
                }
            }
        }
    }

    // Remove suppressed detections
    std::vector<Detection> filteredDetections;
    for (size_t i = 0; i < detections.size(); ++i) {
        if (!suppressed[i]) {
            filteredDetections.push_back(detections[i]);
        }
    }

    detections = std::move(filteredDetections);
    std::cout << "\rNMS后: " << detections.size() << " 个检测结果" << std::endl;
}

float InferenceEngine::CalculateIoU(const Detection& a, const Detection& b) {
    // Convert center coordinates to corner coordinates
    float a_x1 = a.x - a.width / 2.0f;
    float a_y1 = a.y - a.height / 2.0f;
    float a_x2 = a.x + a.width / 2.0f;
    float a_y2 = a.y + a.height / 2.0f;

    float b_x1 = b.x - b.width / 2.0f;
    float b_y1 = b.y - b.height / 2.0f;
    float b_x2 = b.x + b.width / 2.0f;
    float b_y2 = b.y + b.height / 2.0f;

    // Calculate intersection
    float inter_x1 = std::max(a_x1, b_x1);
    float inter_y1 = std::max(a_y1, b_y1);
    float inter_x2 = std::min(a_x2, b_x2);
    float inter_y2 = std::min(a_y2, b_y2);

    if (inter_x2 <= inter_x1 || inter_y2 <= inter_y1) {
        return 0.0f;  // No intersection
    }

    float intersectionArea = (inter_x2 - inter_x1) * (inter_y2 - inter_y1);
    float areaA = a.width * a.height;
    float areaB = b.width * b.height;
    float unionArea = areaA + areaB - intersectionArea;

    return intersectionArea / unionArea;
}

void InferenceEngine::Cleanup() {
    ortSession.reset();
    ortEnv.reset();
    memoryInfo.reset();

    stagingTexture.Reset();
    d3dContext.Reset();
    d3dDevice.Reset();
}
