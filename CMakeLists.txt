cmake_minimum_required(VERSION 3.16)
project(AimBot)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# ?????x64??
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(PLATFORM_X64 TRUE)
else()
    message(FATAL_ERROR "Only x64 platform is supported")
endif()

# ?????????????????????????????????
set(PACKAGES_DIR "${CMAKE_SOURCE_DIR}/packages")
set(DIRECTML_DIR "${PACKAGES_DIR}/Microsoft.AI.DirectML.1.15.4")
set(ONNXRUNTIME_DIR "${PACKAGES_DIR}/Microsoft.ML.OnnxRuntime.DirectML.1.22.1")

# ??????
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/NetConfig
    ${CMAKE_SOURCE_DIR}/src/kmbox
    ${DIRECTML_DIR}/include
    ${ONNXRUNTIME_DIR}/build/native/include
)

# ????
link_directories(
    ${DIRECTML_DIR}/bin/x64-win
    ${ONNXRUNTIME_DIR}/runtimes/win-x64/native
)

# ????
set(SOURCES
    src/main.cpp
    src/screen_capture.cpp
    src/gpu_preprocessor.cpp
    src/gpu_renderer.cpp
    src/inference_engine.cpp
    src/pid_controller.cpp
    src/mouse_controller_factory.cpp
    src/kmbox_controller_new.cpp
    src/kmbox/kmboxNet.cpp
    src/kmbox/my_enc.cpp
    src/imgui_pid_debugger.cpp
    imgui/imgui.cpp
    imgui/imgui_draw.cpp
    imgui/imgui_tables.cpp
    imgui/imgui_widgets.cpp
    imgui/imgui_impl_dx11.cpp
    imgui/imgui_impl_win32.cpp
)

# ?????????????????????????

# ??????????
add_executable(${PROJECT_NAME} ${SOURCES})

# ?????
target_link_libraries(${PROJECT_NAME}
    d3d11
    dxgi
    d3dcompiler
    DirectML
    onnxruntime
    ws2_32
    winmm
)

# ?????????????????????????

# ????DLL???????
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${DIRECTML_DIR}/bin/x64-win/DirectML.dll"
    $<TARGET_FILE_DIR:${PROJECT_NAME}>
)

add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${ONNXRUNTIME_DIR}/runtimes/win-x64/native/onnxruntime.dll"
    $<TARGET_FILE_DIR:${PROJECT_NAME}>
)

add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${ONNXRUNTIME_DIR}/runtimes/win-x64/native/onnxruntime_providers_shared.dll"
    $<TARGET_FILE_DIR:${PROJECT_NAME}>
)

# Windows???????
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
endif()
