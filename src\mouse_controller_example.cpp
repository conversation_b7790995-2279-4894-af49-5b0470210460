#include <iostream>
#include <thread>
#include <chrono>
#include <Windows.h>
#include "mouse_controller.h"

/**
 * @brief 鼠标控制器抽象接口使用示例
 * 
 * 这个示例展示了如何使用抽象的MouseController接口，
 * 可以轻松切换不同的鼠标控制实现方式。
 */

void PrintMenu() {
    std::cout << "\n=== 鼠标控制器抽象接口测试 ===" << std::endl;
    std::cout << "1. 连接控制器" << std::endl;
    std::cout << "2. 测试鼠标移动" << std::endl;
    std::cout << "3. 测试平滑移动" << std::endl;
    std::cout << "4. 测试贝塞尔移动" << std::endl;
    std::cout << "5. 测试鼠标点击" << std::endl;
    std::cout << "6. 启动鼠标监控" << std::endl;
    std::cout << "7. 停止鼠标监控" << std::endl;
    std::cout << "8. 查看鼠标状态" << std::endl;
    std::cout << "9. 切换控制器类型" << std::endl;
    std::cout << "10. 断开连接" << std::endl;
    std::cout << "0. 退出程序" << std::endl;
    std::cout << "请选择操作: ";
}

void OnMouseEvent(const MouseController::MouseState& state) {
    static MouseController::MouseState lastState;
    
    // 只在状态发生变化时输出
    if (state.leftButton != lastState.leftButton) {
        std::cout << "左键: " << (state.leftButton ? "按下" : "松开") << std::endl;
    }
    if (state.rightButton != lastState.rightButton) {
        std::cout << "右键: " << (state.rightButton ? "按下" : "松开") << std::endl;
    }
    if (state.middleButton != lastState.middleButton) {
        std::cout << "中键: " << (state.middleButton ? "按下" : "松开") << std::endl;
    }
    if (state.x != lastState.x || state.y != lastState.y) {
        std::cout << "鼠标位置: (" << state.x << ", " << state.y << ")" << std::endl;
    }
    
    lastState = state;
}

void OnError(int errorCode, const std::string& errorMessage) {
    std::cout << "错误回调: [" << errorCode << "] " << errorMessage << std::endl;
}

std::string SelectControllerType() {
    std::cout << "\n选择鼠标控制器类型:" << std::endl;
    
    auto supportedTypes = MouseControllerFactory::GetSupportedTypes();
    for (size_t i = 0; i < supportedTypes.size(); i++) {
        std::cout << (i + 1) << ". " << supportedTypes[i] << std::endl;
    }
    
    std::cout << "请选择 (1-" << supportedTypes.size() << "): ";
    int choice;
    std::cin >> choice;
    
    if (choice >= 1 && choice <= static_cast<int>(supportedTypes.size())) {
        return supportedTypes[choice - 1];
    }
    
    return "kmbox"; // 默认返回kmbox
}

std::string GetConfigForType(const std::string& type) {
    if (type == "kmbox") {
        // KMBox配置示例
        return R"({
            "ip": "*************",
            "port": "8808",
            "mac": "62587019",
            "monitorPort": 1000,
            "maxRetries": 3,
            "retryDelayMs": 1000,
            "enableEncryption": true
        })";
    }
    else if (type == "system") {
        // 系统控制器配置示例
        return R"({
            "sensitivity": 1.0,
            "smoothing": true
        })";
    }
    else if (type == "logitech") {
        // 罗技控制器配置示例
        return R"({
            "deviceId": "auto",
            "dpi": 800
        })";
    }
    
    return "{}"; // 空配置
}

int main() {
    std::cout << "=== 鼠标控制器抽象接口测试程序 ===" << std::endl;
    std::cout << "这个程序演示了如何使用统一的鼠标控制接口" << std::endl;
    std::cout << "支持多种控制器实现方式的无缝切换" << std::endl;
    
    std::unique_ptr<MouseController> controller;
    std::string currentType = "kmbox";
    
    bool running = true;
    int choice;
    
    while (running) {
        PrintMenu();
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                // 连接控制器
                if (!controller) {
                    controller = MouseControllerFactory::Create(currentType);
                    if (!controller) {
                        std::cout << "✗ 控制器创建失败" << std::endl;
                        break;
                    }
                }
                
                // 设置错误回调
                controller->SetErrorCallback(OnError);
                
                // 获取配置并初始化
                std::string config = GetConfigForType(currentType);
                std::cout << "正在连接 " << currentType << " 控制器..." << std::endl;
                
                if (controller->Initialize(config)) {
                    std::cout << "✓ 控制器连接成功！" << std::endl;
                    std::cout << "类型: " << controller->GetControllerType() << std::endl;
                    std::cout << "版本: " << controller->GetVersion() << std::endl;
                } else {
                    std::cout << "✗ 控制器连接失败: " << controller->GetLastErrorMessage() << std::endl;
                }
                break;
            }
            case 2: {
                // 测试鼠标移动
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                std::cout << "测试基本移动..." << std::endl;
                controller->MoveMouse(50, 0, MouseController::MoveType::Instant);
                Sleep(500);
                controller->MoveMouse(0, 50, MouseController::MoveType::Instant);
                Sleep(500);
                controller->MoveMouse(-50, -50, MouseController::MoveType::Instant);
                std::cout << "✓ 基本移动测试完成" << std::endl;
                break;
            }
            case 3: {
                // 测试平滑移动
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                std::cout << "测试平滑移动..." << std::endl;
                controller->SmoothMoveMouse(100, 100, 1000);
                Sleep(1200);
                controller->SmoothMoveMouse(-100, -100, 1000);
                std::cout << "✓ 平滑移动测试完成" << std::endl;
                break;
            }
            case 4: {
                // 测试贝塞尔移动
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                std::cout << "测试贝塞尔曲线移动..." << std::endl;
                controller->BezierMoveMouse(150, 0, 1500, 50, -50, 100, 50);
                Sleep(1700);
                std::cout << "✓ 贝塞尔移动测试完成" << std::endl;
                break;
            }
            case 5: {
                // 测试鼠标点击
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                std::cout << "测试鼠标点击..." << std::endl;
                controller->ClickMouseButton(MouseController::MouseButton::Left, 100);
                Sleep(200);
                controller->ClickMouseButton(MouseController::MouseButton::Right, 100);
                std::cout << "✓ 鼠标点击测试完成" << std::endl;
                break;
            }
            case 6: {
                // 启动鼠标监控
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                std::cout << "启动鼠标监控..." << std::endl;
                if (controller->StartMonitoring(OnMouseEvent)) {
                    std::cout << "✓ 鼠标监控已启动，移动鼠标或点击按键查看效果" << std::endl;
                } else {
                    std::cout << "✗ 鼠标监控启动失败" << std::endl;
                }
                break;
            }
            case 7: {
                // 停止鼠标监控
                if (controller) {
                    controller->StopMonitoring();
                    std::cout << "✓ 鼠标监控已停止" << std::endl;
                }
                break;
            }
            case 8: {
                // 查看鼠标状态
                if (!controller || !controller->IsConnected()) {
                    std::cout << "请先连接控制器" << std::endl;
                    break;
                }
                
                auto state = controller->GetMouseState();
                std::cout << "\n=== 当前鼠标状态 ===" << std::endl;
                std::cout << "左键: " << (state.leftButton ? "按下" : "松开") << std::endl;
                std::cout << "右键: " << (state.rightButton ? "按下" : "松开") << std::endl;
                std::cout << "中键: " << (state.middleButton ? "按下" : "松开") << std::endl;
                std::cout << "位置: (" << state.x << ", " << state.y << ")" << std::endl;
                std::cout << "滚轮: " << state.wheel << std::endl;
                break;
            }
            case 9: {
                // 切换控制器类型
                std::string newType = SelectControllerType();
                if (newType != currentType) {
                    if (controller) {
                        controller->Disconnect();
                        controller.reset();
                    }
                    currentType = newType;
                    std::cout << "已切换到 " << currentType << " 控制器，请重新连接" << std::endl;
                }
                break;
            }
            case 10: {
                // 断开连接
                if (controller) {
                    controller->Disconnect();
                    std::cout << "✓ 控制器已断开连接" << std::endl;
                }
                break;
            }
            case 0:
                running = false;
                std::cout << "退出程序..." << std::endl;
                break;
            default:
                std::cout << "无效选择，请重新输入" << std::endl;
                break;
        }
        
        if (choice != 0) {
            std::cout << "\n按Enter键继续...";
            std::cin.ignore();
            std::cin.get();
        }
    }
    
    return 0;
}
