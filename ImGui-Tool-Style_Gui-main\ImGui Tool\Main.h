#pragma once

#include "Imgui\\imconfig.h"
#include "Imgui\\imgui.h"
#include "Imgui\\imgui_impl_dx11.h"
#include "Imgui\\imgui_impl_win32.h"
#include "Imgui\\imgui_internal.h"
#include "Imgui\\imstb_rectpack.h"
#include "Imgui\\imstb_textedit.h"
#include "Imgui\\imstb_truetype.h"
#include "Font.h"

#include <time.h>
#include <string.h>
#include <iostream>
#include <tchar.h>
#include <d3d11.h>
#pragma comment(lib,"d3d11.lib")
#pragma warning( disable : 4996 )
