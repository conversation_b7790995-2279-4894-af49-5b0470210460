{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-1fdb4d75e68f8447a852.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Debug-befa8be188f3eaab74b4.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-1fdb4d75e68f8447a852.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-Release-ac591982ec4de354a649.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-1fdb4d75e68f8447a852.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-MinSizeRel-0cc0902df25f5d23a47f.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "AimBot", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-1fdb4d75e68f8447a852.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "AimBot::@6890427a1f51a3e7e1df", "jsonFile": "target-AimBot-RelWithDebInfo-980e4866c00089faa609.json", "name": "AimBot", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-43c4f5c7cffd1b39f47a.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/aim_bot/build", "source": "C:/Users/<USER>/Desktop/aim_bot"}, "version": {"major": 2, "minor": 4}}